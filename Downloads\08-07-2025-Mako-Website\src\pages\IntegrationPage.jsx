import React, { useEffect, useRef } from 'react';
import { 
    FaLayerGroup, FaSyncAlt, FaCloud, FaPuzzlePiece, FaLaptopCode, 
    FaShieldAlt, FaRocket, FaCreditCard, FaSitemap, FaSearch, FaCogs, 
    FaFlask, FaCheckCircle, FaChartLine, FaUsersCog, FaUserShield, FaIndustry, 
    FaHeartbeat, FaShoppingCart, FaLandmark, FaCode, FaNetworkWired, FaCloudversify, FaPython, FaJava, FaNodeJs, FaMicrosoft, FaAws, FaGoogle, FaDatabase, FaBolt, FaCubes, FaProjectDiagram, FaStream, FaServer, FaEnvelopeOpenText, FaCogs as FaCogsAlt, FaJs, FaReact
} from 'react-icons/fa';
import { motion } from 'framer-motion';

const IntegrationPage = () => {
    const heroCanvasRef = useRef(null);

    // --- Data for the page (remains the same) ---
    const integrationServices = [
        { icon: FaLaptopCode, title: "API Development & Integration", description: "We design, build, and manage robust RESTful, GraphQL, and SOAP APIs, ensuring secure, scalable, and well-documented data exchange." },
        { icon: FaPuzzlePiece, title: "Platform & System Integration", description: "We connect your entire business software landscape, including CRM, ERP, and internal databases to eliminate data silos and streamline workflows." },
        { icon: FaSyncAlt, title: "Real-Time Data Synchronization", description: "Ensure absolute data consistency across all platforms with robust webhooks, message queues, and event-driven architectures." },
        { icon: FaCloud, title: "Cloud & On-Premise Integration", description: "Bridge the gap between your legacy infrastructure and modern cloud services like AWS, Azure, and Google Cloud for enhanced flexibility." },
        { icon: FaShoppingCart, title: "E-commerce Integration", description: "Unify your retail operation by integrating platforms like Shopify or Magento with inventory, shipping, and marketing automation tools." },
        { icon: FaCreditCard, title: "Payment Gateway Integration", description: "Securely integrate leading payment gateways like Stripe, PayPal, and Braintree to ensure a smooth and reliable checkout experience." }
    ];
    const integrationProcess = [
        { icon: FaSearch, title: "Discovery & Strategy", description: "Analyzing your architecture and goals." },
        { icon: FaSitemap, title: "API & Data Mapping", description: "Designing the integration blueprint." },
        { icon: FaCogs, title: "Development & Build", description: "Coding the connectors and middleware." },
        { icon: FaFlask, title: "Testing & Validation", description: "Ensuring flawless data integrity." },
        { icon: FaRocket, title: "Deployment & Support", description: "Going live with ongoing maintenance." },
    ];
    const techStack = {
        "Protocols & APIs": [
            { name: 'REST', icon: FaNetworkWired },
            { name: 'GraphQL', icon: FaProjectDiagram },
            { name: 'SOAP', icon: FaEnvelopeOpenText },
            { name: 'gRPC', icon: FaStream },
            { name: 'Webhooks', icon: FaBolt }
        ],
        "Platforms & Middleware": [
            { name: 'MuleSoft', icon: FaCubes },
            { name: 'Zapier', icon: FaCogsAlt },
            { name: 'RabbitMQ', icon: FaServer },
            { name: 'Apache Kafka', icon: FaStream },
            { name: 'Nginx', icon: FaCloudversify }
        ],
        "Cloud Providers": [
            { name: 'AWS (Lambda, S3, API Gateway)', icon: FaAws },
            { name: 'Azure (Functions, Logic Apps)', icon: FaMicrosoft },
            { name: 'Google Cloud (Cloud Functions)', icon: FaGoogle }
        ],
        "Languages & Frameworks": [
            { name: 'JavaScript', icon: FaJs },
            { name: 'React', icon: FaReact },
            { name: 'Next.js', icon: FaLayerGroup },
            { name: 'Node.js', icon: FaNodeJs },
            { name: 'Python', icon: FaPython },
            { name: 'Java (Spring)', icon: FaJava },
            { name: 'Go', icon: FaCode },
            { name: '.NET Core', icon: FaMicrosoft }
        ]
    };
    const businessBenefits = [
        { icon: FaChartLine, title: "Boosted Operational Efficiency", description: "Automate manual data entry and repetitive tasks between systems, freeing up your team to focus on high-value activities." },
        { icon: FaUsersCog, title: "Enhanced Collaboration", description: "Break down departmental silos by ensuring all teams have access to the same real-time information for better-informed decisions." },
        { icon: FaUserShield, title: "Improved Customer Experience", description: "Create a single view of your customer across all systems to deliver highly personalized and consistent experiences." },
        { icon: FaShieldAlt, title: "Superior Data Accuracy", description: "Eliminate human error and ensure data integrity by establishing a single source of truth. Make decisions based on data you can trust." },
    ];
    const industries = ["E-commerce & Retail", "Healthcare & Life Sciences", "FinTech & Banking", "SaaS & Technology", "Manufacturing", "Logistics & Supply Chain"];
    const industryIcons = {
        "E-commerce & Retail": FaShoppingCart,
        "Healthcare & Life Sciences": FaHeartbeat,
        "FinTech & Banking": FaLandmark,
        "SaaS & Technology": FaLaptopCode,
        "Manufacturing": FaIndustry,
        "Logistics & Supply Chain": FaRocket
    };

    // --- Interactive 3D Hero Animation ---
    useEffect(() => {
        let animationFrameId;
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js';
        script.async = true;
        document.body.appendChild(script);

        script.onload = () => {
            if (!heroCanvasRef.current || typeof THREE === 'undefined') return;

            const container = heroCanvasRef.current;
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.z = 5;

            const renderer = new THREE.WebGLRenderer({ canvas: container, alpha: true, antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

            const particlesCount = 700;
            const positions = new Float32Array(particlesCount * 3);
            const linePositions = [];
            const particles = [];

            for (let i = 0; i < particlesCount; i++) {
                const x = (Math.random() - 0.5) * 15;
                const y = (Math.random() - 0.5) * 15;
                const z = (Math.random() - 0.5) * 15;
                positions[i * 3] = x;
                positions[i * 3 + 1] = y;
                positions[i * 3 + 2] = z;
                particles.push(new THREE.Vector3(x, y, z));
            }

            for (let i = 0; i < particlesCount; i++) {
                for (let j = i + 1; j < particlesCount; j++) {
                    const dist = particles[i].distanceTo(particles[j]);
                    if (dist < 1.8) {
                        linePositions.push(particles[i].x, particles[i].y, particles[i].z, particles[j].x, particles[j].y, particles[j].z);
                    }
                }
            }

            const linesGeometry = new THREE.BufferGeometry();
            linesGeometry.setAttribute('position', new THREE.Float32BufferAttribute(linePositions, 3));
            // Updated line color to a brighter blue
            const linesMaterial = new THREE.LineBasicMaterial({ color: 0x38bdf8, transparent: true, opacity: 0.15 });
            const lines = new THREE.LineSegments(linesGeometry, linesMaterial);
            scene.add(lines);

            const particlesGeometry = new THREE.BufferGeometry();
            particlesGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            const particlesMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 0.035, transparent: true, blending: THREE.AdditiveBlending });
            const particleSystem = new THREE.Points(particlesGeometry, particlesMaterial);
            scene.add(particleSystem);

            const mouse = new THREE.Vector2();
            const handleMouseMove = (event) => {
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            };
            window.addEventListener('mousemove', handleMouseMove);

            const clock = new THREE.Clock();
            const animate = () => {
                const elapsedTime = clock.getElapsedTime();
                animationFrameId = requestAnimationFrame(animate);
                particleSystem.rotation.y = elapsedTime * 0.05;
                lines.rotation.y = elapsedTime * 0.05;
                
                camera.position.x += (mouse.x * 0.5 - camera.position.x) * 0.05;
                camera.position.y += (mouse.y * 0.5 - camera.position.y) * 0.05;
                camera.lookAt(scene.position);

                renderer.render(scene, camera);
            };
            animate();

            const handleResize = () => {
                if (!container) return;
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            };
            window.addEventListener('resize', handleResize);

            return () => {
                window.removeEventListener('resize', handleResize);
                window.removeEventListener('mousemove', handleMouseMove);
                cancelAnimationFrame(animationFrameId);
                if (script.parentNode) document.body.removeChild(script);
            };
        };
        
        return () => {
            if (script.parentNode) document.body.removeChild(script);
            cancelAnimationFrame(animationFrameId);
        }
    }, []);

    // --- Scroll Animations ---
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('is-visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 }
        );
        const elements = document.querySelectorAll('.animate-on-scroll');
        elements.forEach((el) => observer.observe(el));
        return () => elements.forEach((el) => observer.unobserve(el));
    }, []);

    return (
        <>
            <style>{`
                .animate-on-scroll { opacity: 0; transition: opacity 0.8s ease-out, transform 0.8s ease-out; }
                .animate-on-scroll.fade-in { transform: translateY(40px); }
                .animate-on-scroll.fade-in-left { transform: translateX(-40px); }
                .animate-on-scroll.fade-in-right { transform: translateX(40px); }
                .animate-on-scroll.is-visible { opacity: 1; transform: translate(0, 0); }
                .process-timeline-item::before {
                    content: '';
                    position: absolute;
                    top: 2.75rem;
                    left: 2.75rem;
                    width: calc(100% - 5.5rem);
                    height: 2px;
                    background-color: rgba(56, 189, 248, 0.2);
                }
                .process-timeline-item:last-child::before { display: none; }
                @media (min-width: 1024px) {
                    .process-timeline-item::before {
                        top: 2.75rem;
                        left: 5.5rem;
                        width: calc(100% - 5.5rem);
                    }
                }
            `}</style>

            <div style={{ backgroundColor: '#012955' }} className="text-slate-200 font-sans overflow-x-hidden">
                
                {/* --- Hero Section --- */}
                <section className="relative h-screen flex items-center justify-center text-center overflow-hidden">
                    <canvas id="hero-canvas" ref={heroCanvasRef} className="absolute top-0 left-0 w-full h-full z-0"></canvas>
                    <div className="absolute top-0 left-0 w-full h-full z-10" style={{ backgroundColor: 'rgba(1, 41, 85, 0.7)' }}></div>
                    <div className="relative z-20 px-4 animate-on-scroll fade-in is-visible">

                        <h1 class="text-4xl text-sky-300 sm:text-5xl md:text-7xl font-extrabold tracking-tight gradient-text mb-8" 
                        >
                Seamless Web Integration  
            </h1>
                        <p className="mt-6 max-w-3xl mx-auto text-lg md:text-xl text-slate-200 leading-relaxed">
                            Connecting your applications, data, and business processes to create a unified, automated, and powerful digital ecosystem.
                        </p>
                    </div>
                </section>

                {/* --- Intro Section --- */}
                <section className="py-20 md:py-28">
                    <div className="max-w-4xl mx-auto px-4 text-center">
                        <div className="animate-on-scroll fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-sky-300 mb-4">Why Web Integration Matters</h2>
                            <p className="text-slate-300 text-lg leading-relaxed">
                                In today's digital landscape, your business relies on a diverse set of applications. Integration is the art and science of making these separate systems communicate and share data. Without it, you're left with data silos, manual workflows, and a fragmented view of your business. We build the digital bridges that unlock true efficiency and automation.
                            </p>
                        </div>
                    </div>
                </section>

                {/* --- Core Services Section --- */}
                <section className="py-20 md:py-28">
                    <div className="max-w-7xl mx-auto px-4">
                        <div className="text-center mb-16 animate-on-scroll fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-sky-300">Our Core Integration Services</h2>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {integrationServices.map((service, index) => (
                                <motion.div
                                    key={index}
                                    className="backdrop-blur-sm border rounded-xl p-8 text-left transition-all duration-300 group border-sky-300/20 hover:border-sky-300/50 focus:outline-none focus:ring-2 focus:ring-sky-300 animate-on-scroll fade-in"
                                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.05)', transitionDelay: `${index * 100}ms` }}
                                    tabIndex={0}
                                    aria-label={service.title}
                                    whileHover={{ scale: 1.04, boxShadow: '0 4px 32px 0 rgba(56,189,248,0.15)' }}
                                    whileFocus={{ scale: 1.04, boxShadow: '0 4px 32px 0 rgba(56,189,248,0.15)' }}
                                    initial={{ opacity: 0, y: 40 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    viewport={{ once: true, amount: 0.2 }}
                                    transition={{ duration: 0.7, delay: index * 0.1 }}
                                    role="region"
                                >
                                    <div className="text-sky-300 text-4xl mb-6"><service.icon /></div>
                                    <h3 className="text-xl font-semibold text-white mb-3">{service.title}</h3>
                                    <p className="text-slate-300 leading-relaxed">{service.description}</p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>
                
                {/* --- Business Benefits Section --- */}
                <section className="py-20 md:py-28">
                    <div className="max-w-7xl mx-auto px-4">
                        <div className="text-center mb-16 animate-on-scroll fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-sky-300">The Business Benefits</h2>
                            <p className="mt-4 max-w-3xl mx-auto text-lg text-slate-300">Effective integration is more than a technical achievement; it's a strategic advantage.</p>
                        </div>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            {businessBenefits.map((benefit, index) => (
                                <motion.div
                                    key={index}
                                    className="flex items-start p-6 rounded-lg animate-on-scroll fade-in-left relative group"
                                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.05)', transitionDelay: `${index * 150}ms` }}
                                    tabIndex={0}
                                    aria-label={benefit.title}
                                    whileHover={{ scale: 1.04, boxShadow: '0 4px 32px 0 rgba(56,189,248,0.15)' }}
                                    whileFocus={{ scale: 1.04, boxShadow: '0 4px 32px 0 rgba(56,189,248,0.15)' }}
                                    initial={{ opacity: 0, x: -40 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    viewport={{ once: true, amount: 0.2 }}
                                    transition={{ duration: 0.7, delay: index * 0.15 }}
                                    role="region"
                                >
                                    <div className="text-sky-300 text-3xl mr-6 mt-1"><benefit.icon /></div>
                                    <div>
                                        <h3 className="text-xl font-semibold text-white mb-2">{benefit.title}</h3>
                                        <p className="text-slate-300">{benefit.description}</p>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>
                
                {/* --- Process Section --- */}
                <section className="py-20 md:py-28">
                    <div className="max-w-7xl mx-auto px-4">
                        <div className="text-center mb-16 animate-on-scroll fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-sky-300">Our Proven Process</h2>
                            <p className="mt-4 max-w-3xl mx-auto text-lg text-slate-300">We follow a structured process to ensure every integration is planned, executed, and supported to the highest standard.</p>
                        </div>
                        <div className="integration-timeline-enhanced relative mx-auto" style={{maxWidth: '800px'}}>
                            <div className="integration-track-line"></div>
                            {integrationProcess.map((step, index) => {
                                const isLeft = index % 2 === 0;
                                const StepIcon = step.icon;
                                return (
                                    <div key={index} className={`integration-item-enhanced ${isLeft ? 'left-side' : 'right-side'}`} style={{position: 'relative', zIndex: 2}}>
                                        <div className="integration-card-enhanced">
                                            <div className="integration-card-content">
                                                <span className="integration-card-number">{index + 1}</span>
                                                <span className="integration-card-icon"><StepIcon /></span>
                                                <h3 className="text-lg font-semibold text-sky-300 mt-3 mb-2">{step.title}</h3>
                                                <p className="mb-0 text-slate-300 text-sm">{step.description}</p>
                                            </div>
                                        </div>
                                        <div className="integration-dot-enhanced"></div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                    <style>{`
                        .integration-timeline-enhanced {
                            position: relative;
                            width: 100%;
                            padding: 2rem 0;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                        }
                        .integration-track-line {
                            position: absolute;
                            top: 0;
                            left: 50%;
                            height: 100%;
                            transform: translateX(-50%);
                            width: 4px;
                            background: linear-gradient(to bottom, #38bdf8, transparent);
                            box-shadow: 0 0 10px #38bdf8, 0 0 20px #38bdf8;
                            border-radius: 2px;
                            z-index: 1;
                        }
                        .integration-item-enhanced {
                            position: relative;
                            width: 100%;
                            display: flex;
                            margin-bottom: 3rem;
                            z-index: 2;
                        }
                        .integration-item-enhanced.right-side {
                            justify-content: flex-end;
                        }
                        .integration-item-enhanced.left-side {
                            justify-content: flex-start;
                        }
                        .integration-card-enhanced {
                            position: relative;
                            padding: 2rem;
                            width: calc(50% - 40px);
                            background-color: rgba(255, 255, 255, 0.07);
                            border: 1px solid rgba(56, 189, 248, 0.15);
                            border-radius: 1.5rem;
                            backdrop-filter: blur(12px);
                            transition: border-color 0.3s ease, box-shadow 0.3s ease;
                            box-shadow: 0 4px 32px 0 rgba(56,189,248,0.07);
                        }
                        .integration-item-enhanced.left-side .integration-card-enhanced {
                            text-align: right;
                        }
                        .integration-card-enhanced::before {
                            content: "";
                            position: absolute;
                            top: 0; left: 0; right: 0; bottom: 0;
                            background: radial-gradient(circle at center, rgba(56, 189, 248, 0.15) 0%, transparent 50%);
                            border-radius: inherit;
                            opacity: 0;
                            transition: opacity 0.5s ease;
                            pointer-events: none;
                        }
                        .integration-item-enhanced:hover .integration-card-enhanced::before {
                            opacity: 1;
                        }
                        .integration-card-content {
                            position: relative;
                            z-index: 2;
                        }
                        .integration-card-number {
                            position: absolute;
                            top: 0.5rem;
                            font-size: 2.5rem;
                            font-weight: 800;
                            color: rgba(56, 189, 248, 0.08);
                            z-index: -1;
                        }
                        .integration-item-enhanced.right-side .integration-card-number { right: 1rem; }
                        .integration-item-enhanced.left-side .integration-card-number { left: 1rem; }
                        .integration-card-icon {
                            font-size: 1.75rem;
                            color: #38bdf8;
                            margin-bottom: 1rem;
                            display: block;
                        }
                        .integration-dot-enhanced {
                            position: absolute;
                            top: 1rem;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 20px;
                            height: 20px;
                            background: #0f172a;
                            border: 4px solid #38bdf8;
                            border-radius: 50%;
                            z-index: 10;
                            box-shadow: 0 0 15px #38bdf8, 0 0 25px #38bdf8;
                        }
                        @media (max-width: 991px) {
                            .integration-track-line {
                                left: 20px;
                                transform: translateX(0);
                            }
                            .integration-item-enhanced {
                                width: auto;
                                margin-left: 50px;
                                justify-content: flex-start;
                            }
                            .integration-item-enhanced.right-side,
                            .integration-item-enhanced.left-side {
                                justify-content: flex-start;
                                width: calc(100% - 50px);
                            }
                            .integration-card-enhanced {
                                width: 100%;
                                text-align: left !important;
                            }
                            .integration-dot-enhanced {
                                left: 20px;
                            }
                        }
                    `}</style>
                </section>

                {/* --- Tech Stack Section --- */}
                <section className="py-20 md:py-28">
                    <div className="max-w-7xl mx-auto px-4">
                        <div className="text-center mb-16 animate-on-scroll fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-sky-300">Technologies We Master</h2>
                            <p className="mt-4 max-w-3xl mx-auto text-lg text-slate-300">We leverage a modern, robust technology stack to build flexible and scalable integrations.</p>
                        </div>
                        <div className="space-y-12">
                            {Object.entries(techStack).map(([category, technologies], catIndex) => (
                                <motion.div
                                    key={category}
                                    className="animate-on-scroll fade-in-right"
                                    style={{transitionDelay: `${catIndex * 150}ms`}}
                                    initial={{ opacity: 0, x: 40 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    viewport={{ once: true, amount: 0.2 }}
                                    transition={{ duration: 0.7, delay: catIndex * 0.15 }}
                                >
                                    <h3 className="text-xl font-semibold text-white mb-6 border-l-4 border-sky-300 pl-4">{category}</h3>
                                    <div className="flex flex-wrap gap-3">
                                        {technologies.map((tech, index) => {
                                            const Icon = tech.icon || FaCheckCircle;
                                            return (
                                                <motion.div
                                                    key={index}
                                                    className="text-slate-200 text-sm font-medium px-4 py-2 rounded-full transition-colors hover:bg-sky-400 hover:text-white cursor-default relative flex items-center gap-2"
                                                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                                                    tabIndex={0}
                                                    aria-label={tech.name}
                                                    whileHover={{ scale: 1.08, backgroundColor: 'rgba(56,189,248,0.8)', color: '#fff' }}
                                                    whileFocus={{ scale: 1.08, backgroundColor: 'rgba(56,189,248,0.8)', color: '#fff' }}
                                                >
                                                    <Icon className="text-sky-300 text-base mr-1" />
                                                    {tech.name}
                                                    <span
                                                        className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 px-2 py-1 text-xs rounded bg-slate-800 text-white opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none z-30 whitespace-nowrap"
                                                        role="tooltip"
                                                    >
                                                        {`About ${tech.name}`}
                                                    </span>
                                                </motion.div>
                                            );
                                        })}
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* --- Industries Section --- */}
                <section className="py-20 md:py-28">
                    <div className="max-w-7xl mx-auto px-4">
                        <div className="text-center mb-16 animate-on-scroll fade-in">
                            <h2 className="text-3xl md:text-4xl font-bold text-sky-300">Serving Diverse Industries</h2>
                            <p className="mt-4 max-w-3xl mx-auto text-lg text-slate-300">Our integration expertise is adaptable, delivering value to businesses across a wide range of sectors.</p>
                        </div>
                        <div className="flex flex-wrap justify-center gap-4 md:gap-6">
                            {industries.map((industry, index) => {
                                const Icon = industryIcons[industry] || FaCheckCircle;
                                return (
                                    <motion.div
                                        key={index}
                                        className="border rounded-lg p-4 flex items-center gap-4 animate-on-scroll fade-in border-white/10 relative group"
                                        style={{ backgroundColor: 'rgba(255, 255, 255, 0.07)', transitionDelay: `${index * 100}ms` }}
                                        tabIndex={0}
                                        aria-label={industry}
                                        whileHover={{ scale: 1.06, boxShadow: '0 4px 32px 0 rgba(56,189,248,0.15)' }}
                                        whileFocus={{ scale: 1.06, boxShadow: '0 4px 32px 0 rgba(56,189,248,0.15)' }}
                                        initial={{ opacity: 0, y: 40 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        viewport={{ once: true, amount: 0.2 }}
                                        transition={{ duration: 0.7, delay: index * 0.1 }}
                                        role="region"
                                    >
                                        <Icon className="text-sky-300 text-2xl" />
                                        <span className="font-medium text-slate-200">{industry}</span>
                                        <span
                                            className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 px-2 py-1 text-xs rounded bg-slate-800 text-white opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none z-30 whitespace-nowrap"
                                            role="tooltip"
                                        >
                                            {`Industry: ${industry}`}
                                        </span>
                                    </motion.div>
                                );
                            })}
                        </div>
                    </div>
                </section>

            </div>
        </>
    );
};

export default IntegrationPage;
