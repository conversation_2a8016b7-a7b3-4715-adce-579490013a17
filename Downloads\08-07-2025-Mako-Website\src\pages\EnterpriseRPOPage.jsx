import { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { Container, Row, Col, Image, ListGroup } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "animate.css";
import Enterprise1 from "../Asserts/Enterprise1.png";
import Enterprise3 from "../Asserts/Enterprise3.png";
import Enterprise2 from "../Asserts/Enterprise2.png";
import Enterprise4 from "../Asserts/Enterprise4.png";

gsap.registerPlugin(ScrollTrigger);

const enterpriseRPOData = {
  hero: {
    title: "Enterprise RPO Services",
    subtitle:
      "<PERSON>re Smart<PERSON>, Grow Faster Expert RPO Services. In this largely competitive environment, organizations must have scalable and innovative recruitment processes to attract top-tier talent.",
    backgroundImage:
      "https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1926&q=80",
  },
  sections: [
    {
      id: "what-is-rpo",
      title: "What Is Recruitment Process Outsourcing ?",
      texts: [
        "Recruitment process outsourcing is a type of business process outsourcing where the entire recruitment process or parts of it is managed by a third-party recruitment and staffing agency.",
        "Makonis is a successful recruitment process outsourcing company that has been providing recruitment and staffing services to several Fortune 500 clients all over the world across sectors.",
      ],
      image:
         Enterprise1,
      alt: "RPO recruitment process outsourcing",
      reversed: false,
    },
    {
      id: "benefits",
      title: "Benefits of RPO Services",
      texts: [""],
      listItems: [
        "Access to Top Talent: We incorporate various recruitment methods and have gathered a quality-rich database of skilled professionals, providing clients access to top talent.",
        "Swift Recruitment: We provide RPO solutions that help organizations acquire skilled candidates swiftly, reducing the time-to-fill significantly.",
        "Cost-effective Solution: We offer RPO services that are scalable, cost-effective, and tailored to the client's needs and comfort.",
        "Better Scalability and Flexibility: Flexible recruitment solutions to keep up with the frequency of hiring, allowing you to scale your workforce up or down as per your needs.",
        // "Ensure Compliance: Expert team who ensure 100% compliance with labor laws, avoiding fines and penalties.",
        // "**Eliminate HR Burden:** Reduces the burden on your in-house HR team, allowing them to focus on strategic tasks.",
        // "**Boost Employer Branding:** We research your brand and competitors to improve your messaging and brand image.",
        // "**Customized Solutions:** From selection process to testing phase and eventual hiring, we provide tailored recruitment solutions."
      ],
      image:
       Enterprise4,
      alt: "Benefits of RPO services",
      reversed: true,
    },
    {
      id: "rpo-models",
      title: "Types of RPO Models",
      texts: [
        "We offer various RPO models to suit different business needs and requirements, from end-to-end solutions to selective outsourcing.",
      ],
      listItems: [
        "End-to-End RPO: All recruitment processes and talent acquisition strategies handled by our RPO team for long-term engagement.",
        "Project RPO: Short-term engagement covering all processes for the duration of specific projects.",
        "Selective RPO: Outsource only certain recruitment functions like candidate sourcing, screening, or recruitment marketing.",
        // "Recruiter on Demand: Flexible model for projects with short timelines and hiring spikes.",
        // "Contingent Recruiter on Demand: Fast-paced process for urgent position filling with payment only on successful hire.",
      ],
      image:
        Enterprise2,
      alt: "Different RPO models and approaches",
      reversed: false,
    },
    {
      id: "technologies",
      title: "Technologies We Use in Our RPO Services",
      texts: [""],
      listItems: [
        "Applicant Tracking Systems (ATS): Makes parsing of resumes and candidate response management simpler, shortlisting candidates at great speed.",
        "Virtual Assistants: Automates screening of candidates and interview scheduling, improving the speed of recruitment process.",
        "Generative AI Tools: Help with writing job descriptions, crafting personalized responses, and preparing interview questionnaires.",
        "Analytics Tools: Understanding recruitment metrics like time to hire, quality of hire, cost per hire, and offer acceptance rate.",
        // "Video Interviewing Platforms: Enables interviewing remote candidates in real-time over video systematically.",
        // "**Social Recruitment Tools:** Help recruit candidates on social media and improve brand reputation.",
        // "**Digital Assessment Tools:** Helps conduct remote assessments with precise measurement of candidate capabilities."
      ],
      image:
         Enterprise3,
      alt: "Technology tools for RPO services",
      reversed: true,
    },
  ],
};

const EnterpriseRPOPage = () => {
  const heroRef = useRef(null);
  const sectionsRef = useRef([]);

  useEffect(() => {
    window.scrollTo(0, 0);

    // GSAP Animations
    const ctx = gsap.context(() => {
      // Hero section animation
      gsap.from(heroRef.current.querySelector("h1"), {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
      });

      gsap.from(heroRef.current.querySelector("p"), {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        delay: 0.3,
      });

      gsap.from(heroRef.current.querySelector(".btn"), {
        y: 30,
        opacity: 0,
        duration: 0.8,
        ease: "back.out(1.7)",
        delay: 0.6,
      });

      // Sections animations
      sectionsRef.current.forEach((section) => {
        if (section) {
          gsap.from(section.querySelector("h2"), {
            y: 50,
            opacity: 0,
            duration: 1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          });

          gsap.from(section.querySelectorAll("p"), {
            y: 30,
            opacity: 0,
            duration: 0.8,
            stagger: 0.2,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 75%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          });

          gsap.from(section.querySelector("img"), {
            scale: 0.8,
            opacity: 0,
            duration: 1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 70%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          });
        }
      });
    }, heroRef);

    return () => ctx.revert();
  }, []);

  const [hoveredImage, setHoveredImage] = useState(null);

  // Reusable Style Objects
  const primaryColor = "#007bff";
  const primaryDarkColor = "#0056b3";
  const primaryRgb = "0,123,255";

  const ctaButtonBaseStyle = {
    padding: "1.2rem 3rem",
    fontSize: "1.2rem",
    background: `linear-gradient(95deg, ${primaryColor}, ${primaryDarkColor})`,
    border: "none",
    borderRadius: "50px",
    boxShadow: `0 8px 25px rgba(${primaryRgb}, 0.3)`,
    transition: "all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1)",
    transform: "translateY(0)",
    color: "#fff",
    textDecoration: "none",
    display: "inline-block",
  };

  const featureImageContainerStyle = (isHovered) => ({
    borderRadius: "1.25rem",
    overflow: "hidden",
    boxShadow: isHovered
      ? "0 1.25rem 3.5rem rgba(0,0,0,0.2)"
      : "0 0.75rem 2rem rgba(0,0,0,0.1)",
    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
    transform: isHovered
      ? "scale(1.04) translateY(-8px)"
      : "scale(1) translateY(0)",
    backgroundColor: "#f0f2f5",
  });

  const featureImageStyle = {
    width: "100%",
    height: "100%",
    minHeight: "400px",
    objectFit: "cover",
    transition: "transform 0.6s ease",
  };

  return (
    <div className="enterprise-rpo-page-wrapper">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="enterprise-rpo-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url(${enterpriseRPOData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating RPO Icons */}
          {[
            { icon: "fa-users-cog", top: "15%", left: "10%", delay: 0 },
            { icon: "fa-chart-bar", top: "25%", right: "15%", delay: 1 },
            { icon: "fa-cogs", bottom: "20%", left: "8%", delay: 2 },
            { icon: "fa-search", bottom: "30%", right: "12%", delay: 3 },
          ].map((item, index) => (
            <div
              key={index}
              className="position-absolute"
              style={{
                ...item,
                width: "60px",
                height: "60px",
                background: "rgba(0, 160, 233, 0.1)",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backdropFilter: "blur(10px)",
                border: "1px solid rgba(0, 160, 233, 0.2)",
                animation: `float 6s ease-in-out infinite`,
                animationDelay: `${item.delay}s`,
              }}
            >
              <i
                className={`fas ${item.icon}`}
                style={{
                  color: "rgba(0, 160, 233, 0.8)",
                  fontSize: "1.5rem",
                }}
              />
            </div>
          ))}

          {/* Gradient Orbs */}
          <div
            className="position-absolute"
            style={{
              width: "300px",
              height: "300px",
              background:
                "radial-gradient(circle, rgba(0, 160, 233, 0.15) 0%, transparent 70%)",
              borderRadius: "50%",
              top: "20%",
              right: "10%",
              filter: "blur(40px)",
              animation: "pulse 4s ease-in-out infinite",
            }}
          />
        </div>
        <Container className="position-relative z-index-1">
          <h1
            style={{
              fontSize: "5rem",
              fontWeight: "800",
              letterSpacing: "2.6px",
              marginBottom: "1rem",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            {enterpriseRPOData.hero.title}
          </h1>
          <p
            className="lead mb-5 mx-auto animate__animated animate__fadeInUp animate__slow"
            style={{
              maxWidth: "800px",
              textShadow: "1px 1px 3px rgba(0,0,0,0.4)",
              fontSize: "1.35rem",
            }}
          >
            {enterpriseRPOData.hero.subtitle}
          </p>
        </Container>
      </section>

      {/* Feature Sections */}
      <div
        className="py-5 py-md-6"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          {enterpriseRPOData.sections.map((section, idx) => (
            <section
              key={section.id}
              ref={(el) => (sectionsRef.current[idx] = el)}
              className="mb-5 mb-md-6 py-3"
            >
              {/* Centered Heading Above Content */}
              <Row>
                <Col xs={12}>
                  <h2 className="text-center"
                    style={{
                      fontSize: "3.6rem",
                      fontWeight: "800",
                      marginBottom:"2.5rem", 
                      letterSpacing: "2.6px",
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                      textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                    }}
                  >
                    {section.title}
                  </h2>
                  {/* Enhanced Accent Line */}
                  <div className="w-30 h-1 mx-auto relative mb-5">
                    <div
                      className="w-full h-full rounded-sm shadow-glow"
                      style={{
                        background:
                          "linear-gradient(90deg, transparent, #00a0e9, transparent)",
                      }}
                    />
                  </div>
                </Col>
              </Row>

              {/* Content and Image Row */}
              <Row
                className={`align-items-center g-4 g-lg-5 ${
                  section.reversed ? "flex-row-reverse" : ""
                }`}
              >
                <Col
                  lg={6}
                  md={12}
                  className={`${section.reversed ? "ps-lg-4" : "pe-lg-4"}`}
                >
                  <div className="content-wrapper">
                    {section.texts.map((text, textIdx) => (
                      <p
                        key={textIdx}
                        className="mb-3 mb-md-4"
                        style={{
                          fontSize: "1.2rem",
                          lineHeight: "1.7",
                          color: "rgba(255, 255, 255, 0.9)",
                         
                        }}
                      >
                        {text}
                      </p>
                    ))}
                    {section.listItems && (
                      <div className="mt-3 mt-md-4 mb-3 mb-md-4">
                        {section.listItems.map((item, itemIdx) => (
                          <div
                            key={itemIdx}
                            className="d-flex align-items-start mb-3"
                            style={{
                              fontSize: "1.2rem",
                              lineHeight: "1.6",
                              color: "rgba(255, 255, 255, 0.85)",
                            }}
                          >
                            <i
                              className="fas fa-check-circle me-3 flex-shrink-0 mt-1"
                              style={{
                                fontSize: "1.1rem",
                                color: "#00a0e9",
                              }}
                            ></i>
                            <span>{item}</span>
                          </div>
                        ))}
                      </div>
                    )}
                   
                  </div>
                </Col>
                <Col lg={6} md={12} className="d-flex justify-content-center">
                  <div
                    className="image-container"
                    style={{
                      ...featureImageContainerStyle(
                        hoveredImage === section.id
                      ),
                      maxWidth: "100%",
                      width: "100%",
                    }}
                    onMouseEnter={() => setHoveredImage(section.id)}
                    onMouseLeave={() => setHoveredImage(null)}
                  >
                    <Image
                      src={section.image}
                      alt={section.alt}
                      fluid
                      className="animate__animated animate__fadeInRight animate__delay-0.5s"
                      style={{
                        ...featureImageStyle,
                        transform:
                          hoveredImage === section.id
                            ? "scale(1.05)"
                            : "scale(1)",
                        height: "350px",
                        objectFit: "cover",
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </section>
          ))}
        </Container>
      </div>

      {/* Global CSS */}
      <style>{`
                :root {
                    --bs-primary: ${primaryColor};
                    --bs-primary-dark: ${primaryDarkColor};
                    --bs-primary-rgb: ${primaryRgb};
                }

                h1, h2, h3, h4, h5, h6 {
                    line-height: 1.2;
                }

                p {
                    line-height: 1.75;
                }

                .container {
                    padding-left: 1.5rem;
                    padding-right: 1.5rem;
                }

                .py-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                .py-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                .py-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }

                .content-wrapper {
                    padding: 0;
                }

                .image-container {
                    position: relative;
                    overflow: hidden;
                }

                @keyframes float {
                    0%, 100% {
                        transform: translateY(0px) rotate(0deg);
                        opacity: 0.6;
                    }
                    33% {
                        transform: translateY(-15px) rotate(120deg);
                        opacity: 1;
                    }
                    66% {
                        transform: translateY(5px) rotate(240deg);
                        opacity: 0.8;
                    }
                }

                @keyframes pulse {
                    0%, 100% {
                        transform: scale(1);
                        opacity: 0.15;
                    }
                    50% {
                        transform: scale(1.1);
                        opacity: 0.25;
                    }
                }

                @media (min-width: 768px) {
                    .py-md-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                    .py-md-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                    .py-md-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }
                    .mb-md-5 { margin-bottom: 4rem !important; }
                    .mb-md-6 { margin-bottom: 6rem !important; }
                    .mb-md-8 { margin-bottom: 8rem !important; }
                }
            `}</style>
    </div>
  );
};

export default EnterpriseRPOPage;
