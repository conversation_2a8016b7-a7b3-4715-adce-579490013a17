import React from 'react';
import { Link } from 'react-router-dom';
import <PERSON>kon<PERSON><PERSON><PERSON> from '../Asserts/Makonis-Logo.png';

// --- Service Icons ---
const AiIcon = () => (
    <svg className="w-5 h-5 mr-3 text-blue-500 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M12 6V3m0 18v-3M5.636 5.636l-.707-.707M18.364 5.636l.707-.707M5.636 18.364l-.707.707M18.364 18.364l.707.707"></path></svg>
);
const DataIcon = () => (
    <svg className="w-5 h-5 mr-3 text-blue-500 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>
);
const IotIcon = () => (
  <svg class="w-5 h-5 mr-3 text-blue-500 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2 1M4 7l2-1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1m2 1l2-1m-2 1V15a2.5 2.5 0 012.5-2.5h3A2.5 2.5 0 0115 15v.5"></path></svg>
);
const WebIcon = () => (
    <svg className="w-5 h-5 mr-3 text-blue-500 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>
);
const Testicon = () => (
  <svg className="w-5 h-5 mr-3 text-blue-500 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path></svg>
);
const EmbIcon = () => ( 
  <svg className="w-5 h-5 mr-3 text-blue-500 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3H4a1 1 0 00-1 1v16a1 1 0 001 1h16a1 1 0 001-1V15M9 3l3-3m0 0l3 3M9 3v7h6M15 21v-4a2 2 0 00-2-2h-2a2 2 0 00-2 2v4"></path></svg>
);

// --- Social Icons ---
const FacebookIcon = () => (<svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" /></svg>);
const InstagramIcon = () => (<svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.024.06 1.378.06 3.808s-.012 2.784-.06 3.808c-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.024.048-1.378.06-3.808.06s-2.784-.013-3.808-.06c-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.048-1.024-.06-1.378-.06-3.808s.012-2.784.06-3.808c.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 016.08 2.525c.636-.247 1.363.416 2.427-.465C9.53 2.013 9.884 2 12.315 2zM12 7a5 5 0 100 10 5 5 0 000-10zm0-2a7 7 0 110 14 7 7 0 010-14zm4.5-1.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" clipRule="evenodd" /></svg>);
const TwitterIcon = () => (<svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.71v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" /></svg>);
const LinkedInIcon = () => (<svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="ture"><path fillRule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clipRule="evenodd" /></svg>);

// --- Data for Links ---
const aboutLinks = [
  { label: 'About Us', href: '/about' },
  { label: 'Our Team', href: '/team' },
  { label: 'Careers', href: '/careers' },
  { label: 'Blog', href: '/blog' }
];

const serviceLinks = [
  { label: 'AI & ML', href: '/products/ai', icon: <AiIcon /> },
  { label: 'Data Analytics', href: '/analytics', icon: <DataIcon /> },
  { label: 'IoT Solutions', href: '/iot', icon: <IotIcon /> },
  { label: 'Web & Mobile', href: '/webdev', icon: <WebIcon /> },
  { label: 'Testing Services', href: '/testing', icon: <Testicon /> },
  { label: 'Embedded Systems', href: '/embedded', icon: <EmbIcon /> },
];

const socialLinks = [
  { label: 'Facebook', href: '#', icon: <FacebookIcon /> },
  { label: 'Instagram', href: '#', icon: <InstagramIcon /> },
  { label: 'Twitter', href: '#', icon: <TwitterIcon /> },
  { label: 'LinkedIn', href: '#', icon: <LinkedInIcon /> }
];

// --- The Main Footer Component ---
const Footer = () => {
  return (
    <footer className="bg-white">
      {/* Upper section with content - reduced padding */}
      <div className="container mx-auto px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 text-gray-800">

          {/* Company Info */}
          <div className="col-span-1">
            <h2 className="text-lg font-bold mb-3 flex items-center">
              <img src={MakonisLogo} alt="Makonis Software Logo" className="h-20 w-auto rounded-md mr-2" />
              <span className="align-middle">Makonis Software</span>
            </h2>
            <p className="text-gray-600 leading-relaxed text-sm">
              Redefining quality through innovative software solutions.
            </p>
          </div>

          {/* About Us Links */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Company</h3>
            <ul className="space-y-2">
              {aboutLinks.map(link => (
                <li key={link.label}>
                  <Link to={link.href} className="text-gray-600 hover:text-blue-600 font-medium transition-colors duration-300 text-sm">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services Links with Icons */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Services</h3>
            <ul className="space-y-2">
              {serviceLinks.map(link => (
                <li key={link.label} className="flex items-center">
                  {link.icon}
                  <Link to={link.href} className="text-gray-600 hover:text-blue-600 font-medium transition-colors duration-300 text-sm">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter Form */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Stay Updated</h3>
            <p className="text-gray-600 mb-3 text-sm">Join our newsletter for the latest in tech.</p>
            <form className="flex">
              <label htmlFor="email-address" className="sr-only">Email address</label>
              <input
                type="email"
                name="email-address"
                id="email-address"
                autoComplete="email"
                required
                className="w-full px-3 py-2 text-sm rounded-l-lg bg-gray-100 border-2 border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
                placeholder="Enter your email"
              />
              <button
                type="submit"
                className="bg-gray-800 hover:bg-black text-white font-bold py-2 px-4 rounded-r-lg transition-colors duration-300"
                aria-label="Subscribe"
              >
                &rarr;
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Lower wave section - reduced padding */}
      <div className="bg-[url('data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20viewBox=%270%200%201440%20320%27%3e%3cpath%20fill=%27%23111827%27%20fill-opacity=%271%27%20d=%27M0,160L80,176C160,192,320,224,480,218.7C640,213,800,171,960,149.3C1120,128,1280,128,1360,128L1440,128L1440,320L1360,320C1280,320,1120,320,960,320C800,320,640,320,480,320C320,320,160,320,80,320L0,320Z%27%3e%3c/path%3e%3c/svg%3e')] bg-cover bg-bottom">
        <div className="container mx-auto px-6 lg:px-8">
            <div className="flex flex-col md:flex-row items-center justify-between pt-16 pb-6">
                 <p className="text-gray-400 text-sm order-2 md:order-1 mt-4 md:mt-0">
                    &copy; {new Date().getFullYear()} Makonis Software Solutions. All Rights Reserved.
                 </p>
                 <div className="flex space-x-6 order-1 md:order-2">
                     {socialLinks.map(social => (
                        <a key={social.label} href={social.href} className="text-gray-400 hover:text-white transition-colors duration-300" aria-label={social.label}>
                            {social.icon}
                        </a>
                     ))}
                 </div>
            </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;