import React, { useEffect, useRef, useState } from "react"; // Corrected: Added useRef and useState
import 'bootstrap/dist/css/bootstrap.min.css';
import ExpertiseImage from "../Asserts/PD3.png"; // Make sure this path is correct
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger'; // Corrected: Import ScrollTrigger

gsap.registerPlugin(ScrollTrigger); // Corrected: Register ScrollTrigger

// Define color constants outside the component for reusability and clarity
const accentRgb = '0, 160, 233'; // RGB values for the accent color
const primaryColor = '#002956'; // Deep Blue - Used for the main background
const secondaryColor = '#0D1B2A'; // Even Darker Blue - Used for the main background

const PhysicalDesignPage = () => {
  const heroRef = useRef(null); // Ref for the main hero section container, crucial for background animations
  const centralChipRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaButtonRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false); // Corrected: useState imported

  useEffect(() => {
    document.title = "Physical Design | Makonis";
  }, []);

  useEffect(() => {
    if (!isLoading) {
      // Corrected: Scope GSAP animations to heroRef using gsap.context
      const ctx = gsap.context(() => {
        // Hero entrance animation with delay for loading
        const tl = gsap.timeline({ delay: 0.3 });

        tl.from(centralChipRef.current, {
          opacity: 0,
          scale: 0.5,
          duration: 1.2,
          ease: "power3.out"
        })
          .from(titleRef.current, {
            y: 100,
            opacity: 0,
            duration: 1,
            ease: "power3.out"
          }, "-=0.8")
          .from(subtitleRef.current, {
            y: 50,
            opacity: 0,
            duration: 0.8,
            ease: "power2.out"
          }, "-=0.7")
          .from(ctaButtonRef.current, { // Animating the hero CTA button
            opacity: 0,
            y: 30,
            duration: 0.7,
            ease: "power2.out"
          }, "-=0.5");

        // GSAP ScrollTrigger for Intro Section
        gsap.from("#intro-section .animated-col", {
          x: (i) => i % 2 === 0 ? -80 : 80,
          opacity: 0,
          duration: 1.2,
          ease: "power3.out",
          stagger: 0.25,
          scrollTrigger: {
            trigger: "#intro-section",
            start: "top 80%",
            toggleActions: "play none none none",
          }
        });
        gsap.from("#intro-section .intro-image-wrapper", {
          opacity: 0,
          scale: 0.8,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: "#intro-section",
            start: "top 75%",
            toggleActions: "play none none none",
          }
        });

        // GSAP ScrollTrigger for Services Header
        gsap.from("#services-section .section-header-animate", {
          y: 70,
          opacity: 0,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: "#services-section",
            start: "top 80%",
            toggleActions: "play none none none",
          }
        });

        // GSAP ScrollTrigger for Service Cards
        gsap.from(".service-card-gsap", {
          y: 60,
          opacity: 0,
          duration: 0.8,
          ease: "power2.out",
          stagger: 0.2,
          scrollTrigger: {
            trigger: "#services-section .row",
            start: "top 75%", // Start animation a bit earlier
            toggleActions: "play none none none",
          }
        });

        // Floating particles in Hero (GSAP animation)
        // Ensure heroRef.current is not null before querying children
        if (heroRef.current) {
          const particles = heroRef.current.querySelectorAll('.hero-particle');
          particles.forEach(particle => {
            gsap.set(particle, {
              x: gsap.utils.random(-window.innerWidth / 2, window.innerWidth / 2),
              y: gsap.utils.random(-window.innerHeight / 2, window.innerHeight / 2),
              scale: gsap.utils.random(0.3, 1),
              opacity: 0,
            });
            gsap.to(particle, {
              x: `+=${gsap.utils.random(-100, 100)}`,
              y: `+=${gsap.utils.random(-100, 100)}`,
              opacity: gsap.utils.random(0.1, 0.5),
              duration: gsap.utils.random(5, 15),
              repeat: -1,
              yoyo: true,
              ease: "sine.inOut",
              delay: gsap.utils.random(0, 5)
            });
          });
        }

      }, heroRef); // Scope GSAP animations to heroRef

      return () => ctx.revert(); // Clean up GSAP animations on unmount
    }
  }, [isLoading]); // Dependency array: run effect when isLoading changes

  return (
    <div style={{ background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)", backdropFilter: "blur(10px)", color: "#ffffff" }}>

      {/* Hero Section */}
      <section
        ref={heroRef} // Attach heroRef here for background animations
        className="d-flex align-items-center position-relative" // Added position-relative
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          minHeight: "520px",
          padding: "60px 20px",
          overflow: "hidden" // Ensure background animations stay within bounds
        }}
      >
        {/* --- Background Animation Elements --- */}
        <div className="position-absolute w-100 h-100 top-0 start-0" style={{ zIndex: 0 }}>
          {/* Floating Particles (Elements that will be animated by GSAP) */}
          {[...Array(50)].map((_, i) => (
            <div
              key={`particle-${i}`}
              className="hero-particle position-absolute"
              style={{
                width: `${Math.random() * 3 + 1}px`,
                height: `${Math.random() * 3 + 1}px`,
                background: `rgba(${accentRgb}, ${Math.random() * 0.5 + 0.2})`,
                borderRadius: '50%',
                boxShadow: `0 0 ${Math.random() * 6 + 2}px rgba(${accentRgb}, 0.5)`,
                left: `${Math.random() * 100}%`, // Initial position, GSAP will take over
                top: `${Math.random() * 100}%`,  // Initial position
              }}
            />
          ))}

          {/* Floating Tech Elements (Elements animated by CSS keyframes) */}
          {[...Array(8)].map((_, i) => (
            <div
              key={`tech-element-${i}`}
              className="floating-tech-element"
              style={{
                top: `${Math.random() * 80 + 10}%`,
                left: `${Math.random() * 80 + 10}%`,
                width: `${30 + Math.random() * 40}px`,
                height: `${30 + Math.random() * 40}px`,
                background: `rgba(${accentRgb}, ${0.05 + Math.random() * 0.1})`,
                borderRadius: i % 3 === 0 ? '50%' : i % 3 === 1 ? '0%' : '25%',
                animationDelay: `${Math.random() * 5}s`,
                border: `1px solid rgba(${accentRgb}, 0.2)`,
              }}
            />
          ))}

          {/* Morphing Background Elements (Elements animated by CSS keyframes) */}
          {[...Array(3)].map((_, i) => (
            <div
              key={`morph-bg-${i}`}
              className="morphing-bg-element"
              style={{
                top: `${20 + i * 25}%`,
                right: `${10 + i * 20}%`,
                width: `${80 + i * 30}px`,
                height: `${80 + i * 30}px`,
                animationDelay: `${i * 3}s`,
              }}
            />
          ))}

          {/* Central Chip Visual Element (This has its own pulse/glow animations) */}
          <div
            ref={centralChipRef} // Attach centralChipRef here
            className="position-absolute glow-pulse"
            style={{
              width: '200px',
              height: '200px',
              zIndex: 1, // Increased zIndex to ensure it's above other bg elements
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -calc(50% + 50px))', // Position above text
              opacity: 0.4,
            }}
          >
            <div className="central-chip-core" style={{
              width: '100%', height: '100%',
              borderRadius: '25px',
              background: `radial-gradient(circle, rgba(${accentRgb}, 0.4) 0%, rgba(${accentRgb}, 0.08) 60%)`,
              boxShadow: `0 0 40px rgba(${accentRgb},0.3), inset 0 0 20px rgba(${accentRgb},0.15)`,
              animation: 'pulseCore 3s infinite ease-in-out', // This animation
              position: 'relative',
              border: `2px solid rgba(${accentRgb}, 0.3)`,
            }}>
              {/* Inner elements for chip */}
              <div style={{ position: 'absolute', top: '25%', left: '25%', width: '50%', height: '50%', background: `rgba(${accentRgb}, 0.3)`, borderRadius: '10px', boxShadow: `0 0 20px rgba(${accentRgb},0.4)`, border: `1px solid rgba(${accentRgb}, 0.5)`, }}></div>
              <div style={{ position: 'absolute', top: '10%', left: '10%', width: '15%', height: '15%', background: `rgba(${accentRgb}, 0.2)`, borderRadius: '50%', boxShadow: `0 0 10px rgba(${accentRgb},0.3)`, }}></div>
              <div style={{ position: 'absolute', top: '10%', right: '10%', width: '15%', height: '15%', background: `rgba(${accentRgb}, 0.2)`, borderRadius: '50%', boxShadow: `0 0 10px rgba(${accentRgb},0.3)`, }}></div>
              <div style={{ position: 'absolute', bottom: '10%', left: '10%', width: '15%', height: '15%', background: `rgba(${accentRgb}, 0.2)`, borderRadius: '50%', boxShadow: `0 0 10px rgba(${accentRgb},0.3)`, }}></div>
              <div style={{ position: 'absolute', bottom: '10%', right: '10%', width: '15%', height: '15%', background: `rgba(${accentRgb}, 0.2)`, borderRadius: '50%', boxShadow: `0 0 10px rgba(${accentRgb},0.3)`, }}></div>
            </div>
            {/* Enhanced circuit lines */}
            {[0, 45, 90, 135, 180, 225, 270, 315].map(angle => (
              <div key={angle} style={{
                position: 'absolute', top: '50%', left: '50%',
                width: '150%', height: '2px',
                background: `linear-gradient(90deg, transparent, rgba(${accentRgb}, 0.3), transparent)`,
                transformOrigin: '0% 50%',
                transform: `translate(0, -1px) rotate(${angle}deg) translateX(50%)`,
                opacity: 0.8,
                boxShadow: `0 0 4px rgba(${accentRgb}, 0.4)`,
              }}></div>
            ))}
          </div>

          {/* Subtle Grid Overlay (This is static but contributes to the background feel) */}
          <div className="position-absolute w-100 h-100 top-0 start-0"
            style={{
              backgroundImage: `
              linear-gradient(rgba(${accentRgb}, 0.03) 1px, transparent 1px),
              linear-gradient(90deg, rgba(${accentRgb}, 0.03) 1px, transparent 1px)
            `,
              backgroundSize: '30px 30px',
              opacity: 0.5,
            }}
          ></div>
        </div>
        {/* --- End Background Animation Elements --- */}

        <div className="container" style={{ zIndex: 2 }}> {/* Increased zIndex to ensure text is above background animations */}
          <div className="row">
            <div className="col-12 text-center text-white">
              <h1
                ref={titleRef} // Attach ref
                className="fw-bold display-5 mb-3"
                style={{
                  fontSize: "4rem",
                  fontWeight: "800",
                  letterSpacing: "2.6px",
                  marginBottom: "1rem",
                  background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                }}
              >Physical Design Services</h1>
              <p
                ref={subtitleRef} // Attach ref
                className="lead text-white mb-4"
              >
                From RTL to GDSII, our expert team delivers low-power, high-performance, and area-efficient SoC implementations. We leverage advanced synthesis, place and route, and timing closure techniques to ensure optimal silicon results.
              </p>
              {/* CTA button */}
              <button
                ref={ctaButtonRef} // Attach ref
                className="btn btn-primary btn-lg mt-3"
                style={{
                  background: `linear-gradient(90deg, rgba(${accentRgb}, 1), #005eff)`,
                  border: 'none',
                  padding: '12px 30px',
                  borderRadius: '50px',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  boxShadow: '0 8px 15px rgba(0,160,233,0.4)',
                  transition: 'all 0.3s ease'
                }}
              >
                Learn More
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Expertise Section - Refined Layout */}
      <section className="py-5" style={{ backgroundColor: "#001e36" }}>
        <div className="container">
          <h2
            className="text-center fw-bold mb-3 section-header-animate" // Added class for ScrollTrigger
            style={{
              fontSize: "3rem",
              fontWeight: "800",
              letterSpacing: "2.6px",
              marginBottom: "1rem",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            Our Expertise
          </h2>
          <p className="text-center text-white mb-5" style={{ maxWidth: "720px", margin: "0 auto" }}>
            From RTL to GDSII, we deliver optimized SoC solutions with precision engineering and domain expertise across all physical design stages.
          </p>

          <div className="row align-items-center g-5">
            {/* Left Visual */}
            <div className="col-lg-6 text-center intro-image-wrapper"> {/* Added class for ScrollTrigger */}
              <div style={{
               padding: "20px",
                borderRadius: "16px",
                background: "linear-gradient(145deg, #0d2a4b, #0c223e)",
                boxShadow: "0 8px 24px rgba(0,160,233,0.1)"
              }}>
                <img
                  src={ExpertiseImage}
                  alt="Chip Visual"
                  className="img-fluid rounded"
                  style={{ maxHeight: "550px", objectFit: "contain" }}
                />
              </div>
            </div>

            {/* Right Cards */}
            <div id="intro-section" className="col-lg-6"> {/* Added id for ScrollTrigger */}
              <div className="row g-4">
                {[
                  {
                    title: "PD Implementation",
                    items: ["Floor Planning", "Place and Route", "Clock Tree Synthesis (CTS)"]
                  },
                  {
                    title: "STA & Design Analysis",
                    items: ["Constraint Generation", "Budgeting", "Timing Sign-off", "Crosstalk, Noise, Signal Integrity", "AOCV and POCV"]
                  },
                  {
                    title: "Physical Verification & DFM",
                    items: ["DRC, LVS, ESD, Antenna Checks", "OPC, CMP, Yield, and Reliability Analysis"]
                  }
                ].map((section, idx) => (
                  <div className="col-md-12 animated-col" key={idx}> {/* Added class for ScrollTrigger */}
                    <div
                      className="p-4 h-100"
                      style={{
                        borderRadius: "14px",
                        background: "rgba(255,255,255,0.04)",
                        border: "1px solid rgba(0,160,233,0.08)",
                        backdropFilter: "blur(12px)",
                        WebkitBackdropFilter: "blur(12px)",
                        boxShadow: "0 4px 12px rgba(0,160,233,0.05)"
                      }}
                    >
                      <h5 className="text-info fw-bold mb-3">{section.title}</h5>
                      <ul className="text-white ps-3 mb-0">
                        {section.items.map((point, j) => (
                          <li key={j}>{point}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Grid-based Process Steps */}
      <section id="services-section" className="py-5" style={{ backgroundColor: "#001e36" }}> {/* Added id for ScrollTrigger */}
        <div className="container">
          <h2 className="text-center text-info fw-bold mb-5 section-header-animate">The Steps We Follow in Our Approach</h2> {/* Added class for ScrollTrigger */}
          <div className="row g-4">
            {[
              {
                step: "01",
                title: "Schematic Extraction",
                desc: "We extract the schematic view of the design and run migration scripts from the source to the destination database."
              },
              {
                step: "02",
                title: "Block Mapping",
                desc: "Verify device mapping for all blocks, simulate with destination models, and create a comparison table."
              },
              {
                step: "03",
                title: "Layout Migration",
                desc: "Migrate layout views with manual tweaks and initiate verification flows after simulation checks."
              },
              {
                step: "04",
                title: "Manual Porting",
                desc: "If mismatches arise, apply manual porting techniques and replicate transistor operating points in new tech."
              },
              {
                step: "05",
                title: "Comparison & Tuning",
                desc: "Tweak and match source characteristics to the destination, ensuring the design meets specifications."
              },
              {
                step: "06",
                title: "Final Netlist Verification",
                desc: "Post-layout netlist is compared with pre-layout simulation to confirm requirement alignment."
              }
            ].map((item, idx) => (
              <div className="col-md-6 col-lg-4 service-card-gsap" key={idx}> {/* Added class for ScrollTrigger */}
                <div
                  className="p-4 h-100 rounded shadow card-hover"
                  style={{
                    backgroundColor: "#0f2b4b",
                    border: "1px solid rgba(0,160,233,0.2)",
                    transition: "transform 0.3s ease, box-shadow 0.3s ease"
                  }}
                >
                  <div className="d-flex align-items-center mb-3">
                    <div
                      className="rounded-circle d-flex align-items-center justify-content-center me-3 step-badge"
                      style={{
                        width: "48px",
                        height: "48px",
                        background: "radial-gradient(circle at top left, #00cfff, #005eff)",
                        color: "#fff",
                        fontWeight: "bold",
                        fontSize: "1.1rem",
                        boxShadow: "0 0 12px rgba(0,160,233,0.5)"
                      }}
                    >
                      {item.step}
                    </div>
                    <h6 className="text-white fw-bold mb-0">{item.title}</h6>
                  </div>
                  <p className="text-white" style={{ fontSize: "0.95rem", lineHeight: "1.6" }}>
                    {item.desc}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <style>
          {`
            .card-hover:hover {
              transform: translateY(-6px);
              box-shadow: 0 12px 24px rgba(0, 160, 233, 0.25);
            }
            @keyframes gradient-animation {
              0% {
                background-position: 0% 50%;
              }
              50% {
                background-position: 100% 50%;
              }
              100% {
                background-position: 0% 50%;
              }
            }
            /* Central Chip Core Pulse */
            @keyframes pulseCore {
                0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(${accentRgb}, 0.4); }
                70% { transform: scale(1); box-shadow: 0 0 0 20px rgba(${accentRgb}, 0); }
                100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(${accentRgb}, 0); }
            }

            /* Enhanced Floating Elements */
            .floating-tech-element {
              position: absolute;
              opacity: 0.1;
              animation: floatTech 12s ease-in-out infinite;
              pointer-events: none;
            }
            @keyframes floatTech {
              0%, 100% {
                transform: translateY(0) rotate(0deg) scale(1);
                opacity: 0.1;
              }
              50% {
                transform: translateY(-40px) rotate(180deg) scale(1.1);
                opacity: 0.3;
              }
            }

            /* Advanced Glow Effects for Central Chip */
            .glow-pulse {
              animation: glowPulse 3s ease-in-out infinite;
            }
            @keyframes glowPulse {
              0%, 100% {
                filter: drop-shadow(0 0 10px rgba(${accentRgb}, 0.3));
              }
              50% {
                filter: drop-shadow(0 0 25px rgba(${accentRgb}, 0.6));
              }
            }

            /* Morphing Background Elements */
            .morphing-bg-element {
              position: absolute;
              background: linear-gradient(45deg, rgba(${accentRgb}, 0.05), rgba(${accentRgb}, 0.15));
              border-radius: 50%;
              animation: morphBg 15s ease-in-out infinite;
              filter: blur(2px);
            }
            @keyframes morphBg {
              0%, 100% {
                border-radius: 50%;
                transform: rotate(0deg) scale(1);
              }
              25% {
                border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
                transform: rotate(90deg) scale(1.2);
              }
              50% {
                border-radius: 20% 80% 20% 80% / 80% 20% 80% 20%;
                transform: rotate(180deg) scale(0.8);
              }
              75% {
                border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
                transform: rotate(270deg) scale(1.1);
              }
            }
          `}
        </style>
      </section>

      {/* Final CTA Section */}
      <section
        className="text-center text-white"
        style={{
          padding: "100px 20px"
        }}
      >
        {/* Your CTA content */}
      </section>
    </div>
  );
};

export default PhysicalDesignPage;