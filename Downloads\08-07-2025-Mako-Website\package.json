{"name": "mako-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "animate.css": "^4.1.1", "animejs": "^4.0.2", "axios": "^1.6.0", "bootstrap": "^5.3.6", "framer-motion": "^10.16.4", "fuse.js": "^7.1.0", "gsap": "^3.13.0", "leaflet": "^1.9.4", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-leaflet": "^4.2.1", "react-modal": "^3.16.3", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5", "swiper": "^11.2.8", "tailwindcss": "^3.4.17", "three": "^0.177.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.21", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.5.4", "vite": "^4.4.5"}}