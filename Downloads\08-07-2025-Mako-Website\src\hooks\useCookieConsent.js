import { useState, useEffect } from 'react';

const COOKIE_CONSENT_KEY = 'makonis_cookie_consent';
const SESSION_BANNER_KEY = 'makonis_banner_shown_session';

export const useCookieConsent = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if banner was already dismissed in this session (temporary)
    const bannerDismissedInSession = sessionStorage.getItem(SESSION_BANNER_KEY);

    // Show banner if it hasn't been dismissed in this session/tab
    // This means it will show in every new tab, regardless of previous consent
    if (!bannerDismissedInSession) {
      setShowBanner(true);
    }

    setIsLoading(false);
  }, []);

  const acceptCookies = () => {
    // Mark as dismissed for this session only
    sessionStorage.setItem(SESSION_BANNER_KEY, 'dismissed');
    // Optionally still track consent in localStorage for analytics
    localStorage.setItem(COOKIE_CONSENT_KEY, 'accepted');
    setShowBanner(false);
  };

  const declineCookies = () => {
    // Mark as dismissed for this session only
    sessionStorage.setItem(SESSION_BANNER_KEY, 'dismissed');
    // Optionally still track decline in localStorage for analytics
    localStorage.setItem(COOKIE_CONSENT_KEY, 'declined');
    setShowBanner(false);
  };

  const resetConsent = () => {
    // Clear both storages and show banner again
    localStorage.removeItem(COOKIE_CONSENT_KEY);
    sessionStorage.removeItem(SESSION_BANNER_KEY);
    setShowBanner(true);
  };

  const hasConsent = () => {
    return localStorage.getItem(COOKIE_CONSENT_KEY) === 'accepted';
  };

  return {
    showBanner,
    isLoading,
    acceptCookies,
    declineCookies,
    resetConsent,
    hasConsent
  };
};
