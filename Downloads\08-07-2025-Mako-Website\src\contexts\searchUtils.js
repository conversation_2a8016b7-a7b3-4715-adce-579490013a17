import Fuse from 'fuse.js';
import { searchData } from './searchData';
 
// Flatten all search data into a single array
const getAllSearchItems = () => {
  const allItems = [];
 
  // Add all categories of search data
  Object.values(searchData).forEach(category => {
    if (Array.isArray(category)) {
      allItems.push(...category);
    }
  });
 
  return allItems;
};
 
// Fuse.js configuration for fuzzy search
const fuseOptions = {
  keys: [
    {
      name: 'title',
      weight: 0.4
    },
    {
      name: 'description',
      weight: 0.3
    },
    {
      name: 'keywords',
      weight: 0.3
    }
  ],
  threshold: 0.3, // Lower = more strict matching
  distance: 100,
  includeScore: true,
  includeMatches: true,
  minMatchCharLength: 2,
  shouldSort: true,
  findAllMatches: true
};
 
// Initialize Fuse instance
const fuse = new Fuse(getAllSearchItems(), fuseOptions);
 
// Main search function
export const performSearch = (query, options = {}) => {
  if (!query || query.trim().length < 2) {
    return {
      results: [],
      totalResults: 0,
      query: query
    };
  }
 
  const {
    maxResults = 10,
    filterByType = null,
    includePopular = false
  } = options;
 
  // Perform the search
  let searchResults = fuse.search(query.trim());
 
  // Filter by type if specified
  if (filterByType) {
    searchResults = searchResults.filter(result =>
      result.item.type === filterByType
    );
  }
 
  // Limit results
  searchResults = searchResults.slice(0, maxResults);
 
  // Format results for display
  const formattedResults = searchResults.map(result => ({
    ...result.item,
    score: result.score,
    matches: result.matches,
    highlightedTitle: highlightMatches(result.item.title, result.matches, 'title'),
    highlightedDescription: highlightMatches(result.item.description, result.matches, 'description')
  }));
 
  return {
    results: formattedResults,
    totalResults: searchResults.length,
    query: query
  };
};
 
// Function to highlight matched text
const highlightMatches = (text, matches, fieldName) => {
  if (!matches || !text) return text;
 
  const fieldMatches = matches.filter(match => match.key === fieldName);
  if (fieldMatches.length === 0) return text;
 
  let highlightedText = text;
  const highlights = [];
 
  fieldMatches.forEach(match => {
    match.indices.forEach(([start, end]) => {
      highlights.push({ start, end });
    });
  });
 
  // Sort highlights by start position (descending) to avoid index shifting
  highlights.sort((a, b) => b.start - a.start);
 
  highlights.forEach(({ start, end }) => {
    const before = highlightedText.substring(0, start);
    const highlighted = highlightedText.substring(start, end + 1);
    const after = highlightedText.substring(end + 1);
    highlightedText = before + `<mark class="search-highlight">${highlighted}</mark>` + after;
  });
 
  return highlightedText;
};
 
// Get search suggestions based on partial input
export const getSearchSuggestions = (query, maxSuggestions = 5) => {
  if (!query || query.trim().length < 1) {
    return getPopularSearches();
  }

  const results = performSearch(query, { maxResults: maxSuggestions });
  return results.results.map(result => ({
    id: result.id,
    title: result.title,
    type: result.type,
    path: result.path,
    icon: getTypeIcon(result.type)
  }));
};
 
// Get popular/recent searches when no query
export const getPopularSearches = () => {
  return [
    { id: 'popular-testing', title: 'Testing Services', type: 'service', path: '/testing' },
    { id: 'popular-ai', title: 'Artificial Intelligence', type: 'service', path: '/analytics' },
    { id: 'popular-iot', title: 'IoT Solutions', type: 'service', path: '/iot' },
    { id: 'popular-team', title: 'Our Team', type: 'navigation', path: '/team' },
    { id: 'popular-contact', title: 'Contact Us', type: 'navigation', path: '/contact' }
  ];
};
 
// Filter search results by category
export const getSearchResultsByCategory = (query) => {
  const allResults = performSearch(query, { maxResults: 50 });
 
  const categorized = {
    navigation: [],
    services: [],
    team: [],
    testimonials: [],
    technologies: [],
    other: []
  };
 
  allResults.results.forEach(result => {
    switch (result.type) {
      case 'navigation':
        categorized.navigation.push(result);
        break;
      case 'service':
      case 'testing-service':
      case 'embedded-service':
      case 'physical-design-service':
      case 'physical-verification-service':
        categorized.services.push(result);
        break;
      case 'team':
        categorized.team.push(result);
        break;
      case 'testimonial':
        categorized.testimonials.push(result);
        break;
      case 'technology':
        categorized.technologies.push(result);
        break;
      default:
        categorized.other.push(result);
    }
  });
 
  return categorized;
};
 
// Get type-specific search results
export const searchByType = (query, type) => {
  return performSearch(query, { filterByType: type, maxResults: 20 });
};
 
// Search analytics (for future implementation)
export const trackSearch = (query, resultCount, selectedResult = null) => {
  // This could be implemented to track search analytics
  console.log('Search tracked:', { query, resultCount, selectedResult });
};
 
// Debounce function for search input
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};
 
// Get content type display name
export const getTypeDisplayName = (type) => {
  const typeMap = {
    'navigation': 'Pages',
    'service': 'Services',
    'testing-service': 'Testing Services',
    'embedded-service': 'Embedded Services',
    'physical-design-service': 'Physical Design',
    'physical-verification-service': 'Physical Verification',
    'team': 'Team Members',
    'testimonial': 'Testimonials',
    'technology': 'Technologies'
  };
 
  return typeMap[type] || 'Other';
};
 
// Get icon for content type
export const getTypeIcon = (type) => {
  const iconMap = {
    'navigation': 'fa-compass',
    'service': 'fa-cogs',
    'testing-service': 'fa-vial',
    'embedded-service': 'fa-microchip',
    'physical-design-service': 'fa-drafting-compass',
    'physical-verification-service': 'fa-check-circle',
    'team': 'fa-users',
    'testimonial': 'fa-quote-right',
    'technology': 'fa-tools'
  };
 
  return iconMap[type] || 'fa-file';
};