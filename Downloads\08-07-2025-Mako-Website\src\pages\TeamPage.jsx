import React, { useState, useEffect } from "react";
import AboutUsAll from "../Asserts/AboutUsAll.png";

const TeamPage = () => {
  const boardMembers = [
    {
      id: 'k<PERSON>hna',
      name: '<PERSON>',
      title: 'Founder & CEO',
      img: 'https://randomuser.me/api/portraits/men/1.jpg',
      bio: '<PERSON> founded the company with a vision to lead HR innovation across Asia. His leadership has been the cornerstone of our success, guiding the group through strategic milestones and fostering a culture of excellence and integrity.',
      zone: { height: '60%', width: '11%', top: '20%', left: '0%' }
    },
    {
      id: 'durga',
      name: '<PERSON><PERSON>',
      title: 'Co-Funder && COO',
      img: 'https://randomuser.me/api/portraits/men/5.jpg',
      bio: 'As the CEO of Recruit Express Group, JS has been instrumental in growing the company into a market leader in HR solutions. Her strategic direction has led to unprecedented growth and expansion into new markets.',
      zone: { height: '60%', width: '11%', top: '20%', left: '11.1%' }
    },
    {
      id: 'sanjay',
      name: '<PERSON><PERSON>',
      title: 'CHRO & Co-Founder - Dubai',
      img: 'https://randomuser.me/api/portraits/men/3.jpg',
      bio: 'Adeline oversees corporate governance and internal operations, bringing structure to the company\'s rapid growth. She ensures that our operational framework is robust, scalable, and aligned with our long-term goals.',
      zone: { height: '60%', width: '11%', top: '20%', left: '22.2%' }
    },
    {
      id: 'Vamshree',
      name: 'Vamshree',
      title: 'CTO & Managing Partner - Australia',
      img: 'https://randomuser.me/api/portraits/men/4.jpg',
      bio: 'Jennifer has led the financial operations of the group, guiding IPO efforts and international expansion. Her financial acumen and strategic planning have been critical in securing the company\'s strong financial position.',
      zone: { height: '60%', width: '11%', top: '20%', left: '33.3%' }
    },
    {
      id: 'sreeni',
      name: 'Sreeni Raju',
      title: 'Head – ERP Software Solutions & Delivery - USA',
      img: 'https://randomuser.me/api/portraits/women/5.jpg',
      bio: 'Heng leads the audit and remuneration committees and ensures financial accountability at all levels. She brings a wealth of experience in financial oversight and corporate governance to the board.',
      zone: { height: '60%', width: '11%', top: '20%', left: '44.4%' }
    },
    {
      id: 'seth',
      name: 'Nishant Seth',
      title: 'Practice Head- Data, AI/ML and Analytics',
      img: 'https://randomuser.me/api/portraits/men/6.jpg',
      bio: 'Pong is the Chairman of the Nominating Committee and has over 20 years of legal and regulatory experience. His expertise is invaluable in maintaining a strong and diverse board composition.',
      zone: { height: '60%', width: '11%', top: '20%', left: '55.5%' }
    },
    {
      id: 'kishore',
      name: 'Kishore Seetharam',
      title: 'Head - Talent Acquisition',
      img: 'https://randomuser.me/api/portraits/men/7.jpg',
      bio: 'Hank contributes his extensive financial audit background to the committee, ensuring accuracy and compliance. His meticulous approach helps uphold the highest standards of financial reporting.',
      zone: { height: '60%', width: '11%', top: '20%', left: '66.6%' }
    },
    {
      id: 'mehala',
      name: 'Dr. Mehala N',
      title: 'Advisor',
      img: 'https://randomuser.me/api/portraits/women/8.jpg',
      bio: 'Albert brings a wealth of HR industry expertise and strategic partnerships from his years in executive recruitment. His insights into market trends and talent acquisition are a significant asset to the board.',
      zone: { height: '60%', width: '11%', top: '20%', left: '77.7%' }
    },
    {
      id: 'vinesh',
      name: 'Vinesh Singh',
      title: 'Head - L&Dr',
      img: 'https://randomuser.me/api/portraits/men/9.jpg',
      bio: 'Esmond is a veteran of governance and risk management, with decades of board-level experience. He provides critical oversight and guidance on complex regulatory and strategic matters.',
      zone: { height: '60%', width: '11%', top: '20%', left: '88.8%' }
    }
  ];

  const [activeMemberId, setActiveMemberId] = useState(null);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleMemberClick = (clickedId) => {
    const previouslyActiveId = activeMemberId;
    setActiveMemberId(activeMemberId === clickedId ? null : clickedId);

    if (clickedId && clickedId !== previouslyActiveId) {
      setTimeout(() => {
        const bioContainer = document.querySelector('.bio-details-container');
        if (bioContainer) {
          bioContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 550);
    }
  };

  const createTeamImageWithZones = () => (
    <div className="relative w-full max-w-6xl mx-auto">
      <img
        src={AboutUsAll}
        alt="Our Leadership Team"
        className="w-full h-auto rounded-lg shadow-2xl hover:shadow-xl transition-shadow duration-300"
      />
      {/* Clickable zones for each team member */}
      {boardMembers.map((member) => (
        <div
          key={member.id}
          className="zone absolute cursor-pointer rounded-lg"
          style={{
            height: member.zone.height,
            width: member.zone.width,
            top: member.zone.top,
            left: member.zone.left,
          }}
          onClick={() => handleMemberClick(member.id)}
          title={`${member.name} - ${member.title}`}
        />
      ))}
    </div>
  );

  const createCardHTML = (member) => {
    const isActive = member.id === activeMemberId;
    return (
      <div
        key={member.id}
        onClick={() => handleMemberClick(member.id)}
        className="member-card cursor-pointer group"
      >
        <div className="flex items-center space-x-2 justify-center">
          <h3 className="font-bold text-lg text-white group-hover:text-orange-400 transition-colors">
            {member.name}
          </h3>
          <div className="text-orange-400 text-2xl font-light">
            {isActive ? '−' : '+'}
          </div>
        </div>
        <p className="text-sm text-orange-400 text-center">{member.title}</p>
      </div>
    );
  };

  const createBioHTML = (member) => (
    <div className="pt-6">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-8 items-center">
        <div className="md:col-span-4">
          <img
            src={member.img}
            alt={member.name}
            className="w-full h-auto rounded-lg object-cover bg-transparent"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = `https://placehold.co/400x400/e0e0e0/757575?text=${member.name.charAt(0)}`;
            }}
          />
        </div>
        <div className="md:col-span-8">
          <h2 className="text-2xl md:text-3xl font-bold text-white">{member.name}</h2>
          <p className="text-md md:text-lg font-semibold text-orange-400 mt-1">{member.title}</p>
          <p className="mt-4 text-gray-300 leading-relaxed">{member.bio}</p>
        </div>
      </div>
    </div>
  );
 
  const renderMembersGrid = () => {
    const result = [];
    let cols = 1;
    if (windowWidth >= 1024) {
      cols = 3;
    } else if (windowWidth >= 768) {
      cols = 2;
    }

    // Find the active member and determine where to insert the bio
    const activeMemberIndex = boardMembers.findIndex(member => member.id === activeMemberId);
    let bioInsertIndex = -1;

    if (activeMemberIndex !== -1) {
      // Calculate the end of the row containing the active member
      const rowNumber = Math.floor(activeMemberIndex / cols);
      bioInsertIndex = Math.min((rowNumber + 1) * cols - 1, boardMembers.length - 1);
    }

    boardMembers.forEach((member, index) => {
      result.push(createCardHTML(member));

      // Insert bio after the last member of the row containing the active member
      if (index === bioInsertIndex && activeMemberId) {
        const activeMember = boardMembers.find(m => m.id === activeMemberId);
        result.push(
          <div key={`bio-${activeMemberId}`} className="bio-details-container active col-span-1 md:col-span-2 lg:col-span-3">
            <div className="bio-content-wrapper">
              {createBioHTML(activeMember)}
            </div>
          </div>
        );
      }
    });

    return result;
  };

  return (
    <div style={{ backgroundColor: '#012854' }} className="text-white font-['Inter',sans-serif]">
      <div className="container mx-auto p-4 md:p-8 max-w-7xl">
        <header className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white text-center">Our Leadership</h1>
        </header>

        <div className="mb-12">
          {createTeamImageWithZones()}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-8">
          {renderMembersGrid()}
        </div>
      </div>
    </div>
  );
};
 
export default TeamPage;