import React, { useState, useEffect } from "react";

const TeamPage = () => {
  const boardMembers = [
    {
      id: 'krishna',
      name: '<PERSON>',
      title: 'Founder & CEO',
      img: 'https://randomuser.me/api/portraits/men/1.jpg',
      bio: '<PERSON> founded the company with a vision to lead HR innovation across Asia. His leadership has been the cornerstone of our success, guiding the group through strategic milestones and fostering a culture of excellence and integrity.'
    },
    {
      id: 'durga',
      name: '<PERSON><PERSON> <PERSON>',
      title: 'Co-Funder && COO',
      img: 'https://randomuser.me/api/portraits/women/2.jpg',
      bio: 'As the CEO of Recruit Express Group, JS has been instrumental in growing the company into a market leader in HR solutions. Her strategic direction has led to unprecedented growth and expansion into new markets.'
    },
    {
      id: 'sanjay',
      name: '<PERSON><PERSON>',
      title: 'CH<PERSON> & Co-Founder - Dubai',
      img: 'https://randomuser.me/api/portraits/women/3.jpg',
      bio: '<PERSON><PERSON><PERSON> oversees corporate governance and internal operations, bringing structure to the company\'s rapid growth. She ensures that our operational framework is robust, scalable, and aligned with our long-term goals.'
    },
    {
      id: '<PERSON><PERSON><PERSON><PERSON>',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      title: 'CTO & Managing Partner - Australia',
      img: 'https://randomuser.me/api/portraits/women/4.jpg',
      bio: 'Jennifer has led the financial operations of the group, guiding IPO efforts and international expansion. Her financial acumen and strategic planning have been critical in securing the company\'s strong financial position.'
    },
    {
      id: 'sreeni',
      name: 'Sreeni Raju',
      title: 'Head – ERP Software Solutions & Delivery - USA',
      img: 'https://randomuser.me/api/portraits/women/5.jpg',
      bio: 'Heng leads the audit and remuneration committees and ensures financial accountability at all levels. She brings a wealth of experience in financial oversight and corporate governance to the board.'
    },
    {
      id: 'seth',
      name: 'Nishant Seth',
      title: 'Practice Head- Data, AI/ML and Analytics',
      img: 'https://randomuser.me/api/portraits/men/6.jpg',
      bio: 'Pong is the Chairman of the Nominating Committee and has over 20 years of legal and regulatory experience. His expertise is invaluable in maintaining a strong and diverse board composition.'
    },
    {
      id: 'kishore',
      name: 'Kishore Seetharam',
      title: 'Head - Talent Acquisition',
      img: 'https://randomuser.me/api/portraits/men/7.jpg',
      bio: 'Hank contributes his extensive financial audit background to the committee, ensuring accuracy and compliance. His meticulous approach helps uphold the highest standards of financial reporting.'
    },
    {
      id: 'mehala',
      name: 'Dr. Mehala N',
      title: 'Advisor',
      img: 'https://randomuser.me/api/portraits/men/8.jpg',
      bio: 'Albert brings a wealth of HR industry expertise and strategic partnerships from his years in executive recruitment. His insights into market trends and talent acquisition are a significant asset to the board.'
    },
    {
      id: 'vinesh',
      name: 'Vinesh Singh',
      title: 'Head - L&Dr',
      img: 'https://randomuser.me/api/portraits/men/9.jpg',
      bio: 'Esmond is a veteran of governance and risk management, with decades of board-level experience. He provides critical oversight and guidance on complex regulatory and strategic matters.'
    },
    {
      id: 'sai',
      name: 'Sai Krishna',
      title: 'Head - Product & Innovation',
      img: 'https://randomuser.me/api/portraits/men/9.jpg',
      bio: 'Esmond is a veteran of governance and risk management, with decades of board-level experience. He provides critical oversight and guidance on complex regulatory and strategic matters.'
    }
  ];

  const [activeMemberId, setActiveMemberId] = useState(null);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleMemberClick = (clickedId) => {
    const previouslyActiveId = activeMemberId;
    setActiveMemberId(activeMemberId === clickedId ? null : clickedId);

    if (clickedId && clickedId !== previouslyActiveId) {
      setTimeout(() => {
        const bioContainer = document.querySelector('.bio-details-container');
        if (bioContainer) {
          bioContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 550);
    }
  };

  const createPhotoHTML = (member) => (
    <img
      key={member.id}
      src={member.img}
      alt={member.name}
      onClick={() => handleMemberClick(member.id)}
      onError={(e) => {
        e.target.onerror = null;
        e.target.src = `https://placehold.co/200x200/e0e0e0/757575?text=${member.name.charAt(0)}`;
      }}
      className="w-16 h-16 md:w-20 md:h-20 rounded-full object-cover cursor-pointer transition-transform duration-300 hover:scale-110"
    />
  );

  const createCardHTML = (member) => {
    const isActive = member.id === activeMemberId;
    return (
      <div
        key={member.id}
        onClick={() => handleMemberClick(member.id)}
        className="member-card cursor-pointer group"
      >
        <div className="flex items-center space-x-2">
          <h3 className="font-bold text-lg text-white group-hover:text-orange-400 transition-colors">
            {member.name}
          </h3>
          <div className="text-orange-400 text-2xl font-light">
            {isActive ? '−' : '+'}
          </div>
        </div>
        <p className="text-sm text-orange-400">{member.title}</p>
      </div>
    );
  };

  const createBioHTML = (member) => (
    <div className="pt-6">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-8 items-center">
        <div className="md:col-span-4">
          <img
            src={member.img}
            alt={member.name}
            className="w-full h-auto rounded-lg object-cover"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = `https://placehold.co/400x400/e0e0e0/757575?text=${member.name.charAt(0)}`;
            }}
          />
        </div>
        <div className="md:col-span-8">
          <h2 className="text-2xl md:text-3xl font-bold text-white">{member.name}</h2>
          <p className="text-md md:text-lg font-semibold text-orange-400 mt-1">{member.title}</p>
          <p className="mt-4 text-gray-300 leading-relaxed">{member.bio}</p>
        </div>
      </div>
    </div>
  );
 
  const renderMembersGrid = () => {
    const result = [];
    let cols = 1;
    if (windowWidth >= 1024) {
      cols = 3;
    } else if (windowWidth >= 768) {
      cols = 2;
    }

    // Find the active member and determine where to insert the bio
    const activeMemberIndex = boardMembers.findIndex(member => member.id === activeMemberId);
    let bioInsertIndex = -1;

    if (activeMemberIndex !== -1) {
      // Calculate the end of the row containing the active member
      const rowNumber = Math.floor(activeMemberIndex / cols);
      bioInsertIndex = Math.min((rowNumber + 1) * cols - 1, boardMembers.length - 1);
    }

    boardMembers.forEach((member, index) => {
      result.push(createCardHTML(member));

      // Insert bio after the last member of the row containing the active member
      if (index === bioInsertIndex && activeMemberId) {
        const activeMember = boardMembers.find(m => m.id === activeMemberId);
        result.push(
          <div key={`bio-${activeMemberId}`} className="bio-details-container active col-span-1 md:col-span-2 lg:col-span-3">
            <div className="bio-content-wrapper">
              {createBioHTML(activeMember)}
            </div>
          </div>
        );
      }
    });

    return result;
  };

  return (
    <div style={{ backgroundColor: '#012854' }} className="text-white font-['Inter',sans-serif]">
      <div className="container mx-auto p-4 md:p-8 max-w-7xl">
        <header className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white text-center">Our Leadership</h1>
        </header>

        <div className="mb-8">
          <div className="flex justify-center items-center gap-3 sm:gap-4 flex-wrap">
            {boardMembers.map(member => createPhotoHTML(member))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-8">
          {renderMembersGrid()}
        </div>
      </div>
    </div>
  );
};
 
export default TeamPage;