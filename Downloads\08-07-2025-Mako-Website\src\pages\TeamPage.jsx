import React, { useState, useEffect } from "react";
import AboutUsAll from "../Asserts/AboutUsAll.png";

const TeamPage = () => {
  const boardMembers = [
    {
      id: 'k<PERSON>hna',
      name: '<PERSON>',
      title: 'Founder & CEO',
      img: 'https://randomuser.me/api/portraits/men/1.jpg',
      bio: '<PERSON> founded the company with a vision to lead HR innovation across Asia. His leadership has been the cornerstone of our success, guiding the group through strategic milestones and fostering a culture of excellence and integrity.',
      zone: { height: '60%', width: '25%', top: '20%', left: '0%' }
    },
    {
      id: 'durga',
      name: '<PERSON><PERSON>',
      title: 'Co-Founder & COO',
      img: 'https://randomuser.me/api/portraits/men/5.jpg',
      bio: 'As the COO of our organization, <PERSON><PERSON> has been instrumental in growing the company into a market leader in HR solutions. His strategic direction has led to unprecedented growth and expansion into new markets.',
      zone: { height: '60%', width: '25%', top: '20%', left: '25%' }
    },
    {
      id: 'sanjay',
      name: '<PERSON><PERSON>',
      title: 'CHRO & Co-Founder - Dubai',
      img: 'https://randomuser.me/api/portraits/men/3.jpg',
      bio: '<PERSON>jay oversees corporate governance and internal operations, bringing structure to the company\'s rapid growth. He ensures that our operational framework is robust, scalable, and aligned with our long-term goals.',
      zone: { height: '60%', width: '25%', top: '20%', left: '50%' }
    },
    {
      id: 'vinesh',
      name: 'Vinesh Singh',
      title: 'Head - L&D',
      img: 'https://randomuser.me/api/portraits/men/9.jpg',
      bio: 'Vinesh is a veteran of governance and risk management, with decades of board-level experience. He provides critical oversight and guidance on complex regulatory and strategic matters.',
      zone: { height: '60%', width: '25%', top: '20%', left: '75%' }
    }
  ];

  const [activeMemberId, setActiveMemberId] = useState(null);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleMemberClick = (clickedId) => {
    const previouslyActiveId = activeMemberId;
    setActiveMemberId(activeMemberId === clickedId ? null : clickedId);

    if (clickedId && clickedId !== previouslyActiveId) {
      setTimeout(() => {
        const bioContainer = document.querySelector('.bio-details-container');
        if (bioContainer) {
          bioContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 550);
    }
  };

  const createTeamImageWithZones = () => (
    <div className="relative w-full max-w-6xl mx-auto">
      <img
        src={AboutUsAll}
        alt="Our Leadership Team"
        className="w-full h-auto rounded-lg shadow-2xl hover:shadow-xl transition-shadow duration-300"
      />
      {/* Clickable zones for each team member */}
      {boardMembers.map((member) => (
        <div
          key={member.id}
          className="zone absolute cursor-pointer rounded-lg"
          style={{
            height: member.zone.height,
            width: member.zone.width,
            top: member.zone.top,
            left: member.zone.left,
          }}
          onClick={() => handleMemberClick(member.id)}
          title={`${member.name} - ${member.title}`}
        />
      ))}
    </div>
  );

  const createCardHTML = (member) => {
    const isActive = member.id === activeMemberId;
    return (
      <div
        key={member.id}
        onClick={() => handleMemberClick(member.id)}
        className="member-card cursor-pointer group"
      >
        <div className="flex items-center space-x-2 justify-center">
          <h3 className="font-bold text-lg text-white group-hover:text-orange-400 transition-colors">
            {member.name}
          </h3>
          <div className="text-orange-400 text-2xl font-light">
            {isActive ? '−' : '+'}
          </div>
        </div>
        <p className="text-sm text-orange-400 text-center">{member.title}</p>
      </div>
    );
  };

  const createBioHTML = (member) => (
    <div className="pt-6">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-8 items-center">
        <div className="md:col-span-4">
          <img
            src={member.img}
            alt={member.name}
            className="w-full h-auto rounded-lg object-cover bg-transparent"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = `https://placehold.co/400x400/e0e0e0/757575?text=${member.name.charAt(0)}`;
            }}
          />
        </div>
        <div className="md:col-span-8">
          <h2 className="text-2xl md:text-3xl font-bold text-white">{member.name}</h2>
          <p className="text-md md:text-lg font-semibold text-orange-400 mt-1">{member.title}</p>
          <p className="mt-4 text-gray-300 leading-relaxed">{member.bio}</p>
        </div>
      </div>
    </div>
  );
 
  const renderMembersGrid = () => {
    const result = [];
    let cols = 1;
    if (windowWidth >= 1024) {
      cols = 3;
    } else if (windowWidth >= 768) {
      cols = 2;
    }

    // Find the active member and determine where to insert the bio
    const activeMemberIndex = boardMembers.findIndex(member => member.id === activeMemberId);
    let bioInsertIndex = -1;

    if (activeMemberIndex !== -1) {
      // Calculate the end of the row containing the active member
      const rowNumber = Math.floor(activeMemberIndex / cols);
      bioInsertIndex = Math.min((rowNumber + 1) * cols - 1, boardMembers.length - 1);
    }

    boardMembers.forEach((member, index) => {
      result.push(createCardHTML(member));

      // Insert bio after the last member of the row containing the active member
      if (index === bioInsertIndex && activeMemberId) {
        const activeMember = boardMembers.find(m => m.id === activeMemberId);
        result.push(
          <div key={`bio-${activeMemberId}`} className="bio-details-container active col-span-1 md:col-span-2 lg:col-span-3">
            <div className="bio-content-wrapper">
              {createBioHTML(activeMember)}
            </div>
          </div>
        );
      }
    });

    return result;
  };

  return (
    <div style={{ backgroundColor: '#012854' }} className="text-white font-['Inter',sans-serif]">
      <div className="container mx-auto p-4 md:p-8 max-w-7xl">
        <header className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white text-center">Our Leadership</h1>
        </header>

        <div className="mb-12">
          {createTeamImageWithZones()}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-8">
          {renderMembersGrid()}
        </div>
      </div>
    </div>
  );
};
 
export default TeamPage;