import React, { useState } from "react";

const TeamPage = () => {
  const boardMembers = [
    {
      id: 'peter',
      name: '<PERSON>',
      title: 'Executive Director & Founding Chairman',
      img: 'https://randomuser.me/api/portraits/men/1.jpg',
      bio: '<PERSON> founded the company with a vision to lead HR innovation across Asia. His leadership has been the cornerstone of our success, guiding the group through strategic milestones and fostering a culture of excellence and integrity.'
    },
    {
      id: 'js',
      name: '<PERSON><PERSON>',
      title: 'Executive Director & CEO, Recruit Express',
      img: 'https://randomuser.me/api/portraits/women/2.jpg',
      bio: 'As the CEO of Recruit Express Group, JS has been instrumental in growing the company into a market leader in HR solutions. Her strategic direction has led to unprecedented growth and expansion into new markets.'
    },
    {
      id: 'adeline',
      name: '<PERSON><PERSON><PERSON>',
      title: 'Executive Director & Chief Corporate Officer',
      img: 'https://randomuser.me/api/portraits/women/3.jpg',
      bio: '<PERSON><PERSON><PERSON> oversees corporate governance and internal operations, bringing structure to the company\'s rapid growth. She ensures that our operational framework is robust, scalable, and aligned with our long-term goals.'
    },
    {
      id: 'jennifer',
      name: '<PERSON>',
      title: 'Executive Director & Group CFO',
      img: 'https://randomuser.me/api/portraits/women/4.jpg',
      bio: 'Jennifer has led the financial operations of the group, guiding IPO efforts and international expansion. Her financial acumen and strategic planning have been critical in securing the company\'s strong financial position.'
    },
    {
      id: 'heng',
      name: 'Heng Su-Ling Mae',
      title: 'Lead Independent Director, Audit Chair',
      img: 'https://randomuser.me/api/portraits/women/5.jpg',
      bio: 'Heng leads the audit and remuneration committees and ensures financial accountability at all levels. She brings a wealth of experience in financial oversight and corporate governance to the board.'
    },
    {
      id: 'pong',
      name: 'Pong Chen Yih',
      title: 'Independent Director, Nominating Chair',
      img: 'https://randomuser.me/api/portraits/men/6.jpg',
      bio: 'Pong is the Chairman of the Nominating Committee and has over 20 years of legal and regulatory experience. His expertise is invaluable in maintaining a strong and diverse board composition.'
    },
    {
      id: 'hank',
      name: 'Hank Sato',
      title: 'Independent Director, Audit Committee',
      img: 'https://randomuser.me/api/portraits/men/7.jpg',
      bio: 'Hank contributes his extensive financial audit background to the committee, ensuring accuracy and compliance. His meticulous approach helps uphold the highest standards of financial reporting.'
    },
    {
      id: 'albert',
      name: 'Albert Ellis',
      title: 'Independent Director',
      img: 'https://randomuser.me/api/portraits/men/8.jpg',
      bio: 'Albert brings a wealth of HR industry expertise and strategic partnerships from his years in executive recruitment. His insights into market trends and talent acquisition are a significant asset to the board.'
    },
    {
      id: 'esmond',
      name: 'Esmond Choo Liong Gee',
      title: 'Independent Non-Executive Director',
      img: 'https://randomuser.me/api/portraits/men/9.jpg',
      bio: 'Esmond is a veteran of governance and risk management, with decades of board-level experience. He provides critical oversight and guidance on complex regulatory and strategic matters.'
    }
  ];

  const [activeMemberId, setActiveMemberId] = useState(null);

  const handleMemberClick = (clickedId) => {
    const previouslyActiveId = activeMemberId;
    setActiveMemberId(activeMemberId === clickedId ? null : clickedId);

    if (clickedId && clickedId !== previouslyActiveId) {
      setTimeout(() => {
        const bioContainer = document.querySelector('.bio-details-container');
        if (bioContainer) {
          bioContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 550);
    }
  };

  const createPhotoHTML = (member) => (
    <img
      key={member.id}
      src={member.img}
      alt={member.name}
      onClick={() => handleMemberClick(member.id)}
      onError={(e) => {
        e.target.onerror = null;
        e.target.src = `https://placehold.co/200x200/e0e0e0/757575?text=${member.name.charAt(0)}`;
      }}
      className="w-16 h-16 md:w-20 md:h-20 rounded-full object-cover cursor-pointer transition-transform duration-300 hover:scale-110"
    />
  );

  const createCardHTML = (member) => {
    const isActive = member.id === activeMemberId;
    return (
      <div
        key={member.id}
        onClick={() => handleMemberClick(member.id)}
        className="member-card cursor-pointer group"
      >
        <div className="flex items-center space-x-2">
          <h3 className="font-bold text-lg text-slate-800 group-hover:text-orange-600 transition-colors">
            {member.name}
          </h3>
          <div className="text-orange-600 text-2xl font-light">
            {isActive ? '−' : '+'}
          </div>
        </div>
        <p className="text-sm text-orange-600">{member.title}</p>
      </div>
    );
  };

  const createBioHTML = (member) => (
    <div className="pt-6">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-8 items-center">
        <div className="md:col-span-4">
          <img
            src={member.img}
            alt={member.name}
            className="w-full h-auto rounded-lg object-cover"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = `https://placehold.co/400x400/e0e0e0/757575?text=${member.name.charAt(0)}`;
            }}
          />
        </div>
        <div className="md:col-span-8">
          <h2 className="text-2xl md:text-3xl font-bold text-slate-800">{member.name}</h2>
          <p className="text-md md:text-lg font-semibold text-orange-600 mt-1">{member.title}</p>
          <p className="mt-4 text-slate-600 leading-relaxed">{member.bio}</p>
        </div>
      </div>
    </div>
  );
 
  const renderMembersGrid = () => {
    const result = [];
    let cols = 1;
    if (typeof window !== 'undefined') {
      if (window.innerWidth >= 1024) {
        cols = 3;
      } else if (window.innerWidth >= 768) {
        cols = 2;
      }
    }

    boardMembers.forEach((member, index) => {
      result.push(createCardHTML(member));

      if (activeMemberId === member.id) {
        const activeIndex = index;
        const endOfRowIndex = Math.min((Math.floor(activeIndex / cols) + 1) * cols, boardMembers.length);
        const isLastInRow = index === endOfRowIndex - 1 || index === boardMembers.length - 1;

        if (isLastInRow) {
          result.push(
            <div key={`bio-${member.id}`} className="bio-details-container col-span-1 md:col-span-2 lg:col-span-3 grid grid-rows-[1fr] transition-all duration-500 ease-in-out">
              <div className="overflow-hidden">
                {createBioHTML(member)}
              </div>
            </div>
          );
        }
      }
    });

    return result;
  };

  return (
    <div className="bg-white text-slate-800 font-['Inter',sans-serif]">
      <div className="container mx-auto p-4 md:p-8 max-w-7xl">
        <header className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-slate-900">Board of Directors</h1>
        </header>

        <div className="mb-8">
          <div className="flex justify-center items-center gap-3 sm:gap-4 flex-wrap">
            {boardMembers.map(member => createPhotoHTML(member))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-8">
          {renderMembersGrid()}
        </div>
      </div>
    </div>
  );
};
 
export default TeamPage;