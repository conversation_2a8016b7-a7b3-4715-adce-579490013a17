import React, { useState, useEffect } from "react";
import KrishanaImg from "../Asserts/team_members/Krishana.png";
import DurgaImg from "../Asserts/team_members/Durga.png";
import SanjayImg from "../Asserts/team_members/snajay.png";
import Team from "../Asserts/team_members/team_full.png";


const TeamPage = () => {
  const boardMembers = [
    {
      id: 'krishna',
      name: '<PERSON>',
      title: 'Founder & CEO',
      img: <PERSON><PERSON>mg,
      bio: 'Serial entrepreneur, over 20 years of experience managing technology business.',
      zone: { height: '70%', width: '20%', top: '20%', left: '5.1%' }
    },
    {
      id: 'durga',
      name: '<PERSON><PERSON> <PERSON>',
      title: 'Co-Founder & COO',
      img: DurgaImg,
      bio: 'Over 28 years of experience in global software delivery, vision, strategy, and operational excellence.',
      zone: { height: '70%', width: '20%', top: '20%', left: '33.3%' }
    },
    {
      id: 'sanjay',
      name: '<PERSON><PERSON>',
      title: 'CH<PERSON> & Co-Founder - Dubai',
      img: <PERSON><PERSON>,
      bio: '30 years in global HR functions for IT (products/projects) and retail sectors.',
      zone: { height: '70%', width: '20%', top: '20%', left: '54.4%' }
    },
  ];
  

  const [activeMemberId, setActiveMemberId] = useState(null);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleMemberClick = (clickedId) => {
    const previouslyActiveId = activeMemberId;
    setActiveMemberId(activeMemberId === clickedId ? null : clickedId);

    if (clickedId && clickedId !== previouslyActiveId) {
      setTimeout(() => {
        const bioContainer = document.querySelector('.bio-details-container');
        if (bioContainer) {
          bioContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 550);
    }
  };

  const createTeamImageWithZones = () => (
    <div className="relative w-full max-w-6xl mx-auto">
      <img
        src={Team}
        alt="Our Leadership Team"
        className="w-full h-auto max-h-82 object-cover rounded-lg shadow-2xl hover:shadow-xl transition-shadow duration-300"
      />
      {/* Clickable zones for each team member */}
      {boardMembers.map((member) => (
        <div
          key={member.id}
          className="zone absolute cursor-pointer rounded-lg"
          style={{
            height: member.zone.height,
            width: member.zone.width,
            top: member.zone.top,
            left: member.zone.left,
          }}
          onClick={() => handleMemberClick(member.id)}
          title={`${member.name} - ${member.title}`}
        />
      ))}
    </div>
  );

  const createCardHTML = (member) => {
    const isActive = member.id === activeMemberId;
    return (
      <div
        key={member.id}
        onClick={() => handleMemberClick(member.id)}
        className="member-card cursor-pointer group"
      >
        <div className="flex items-center space-x-2 justify-center">
          <h3 className="font-bold text-lg text-white group-hover:text-orange-400 transition-colors">
            {member.name}
          </h3>
          <div className="text-orange-400 text-2xl font-light">
            {isActive ? '−' : '+'}
          </div>
        </div>
        <p className="text-sm text-orange-400 text-center">{member.title}</p>
      </div>
    );
  };

  const createBioHTML = (member) => (
    <div className="pt-3 flex justify-center w-full">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-8 items-center max-w-3xl w-full px-4">
        <div className="md:col-span-4">
          <img
            src={member.img}
            alt={member.name}
            className="w-full h-auto rounded-lg object-cover bg-transparent"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = `https://placehold.co/400x400/e0e0e0/757575?text=${member.name.charAt(0)}`;
            }}
          />
        </div>
        <div className="md:col-span-8">
          <h2 className="text-2xl md:text-3xl font-bold text-white">{member.name}</h2>
          <p className="text-md md:text-lg font-semibold text-orange-400 mt-1">{member.title}</p>
          <p className="mt-4 text-gray-300 leading-relaxed">{member.bio}</p>
        </div>
      </div>
    </div>
  );
  
  
  const renderMembersGrid = () => {
    const result = [];
    let cols = 1;
    if (windowWidth >= 1024) {
      cols = 4;
    } else if (windowWidth >= 768) {
      cols = 3;
    }

    // Find the active member and determine where to insert the bio
    const activeMemberIndex = boardMembers.findIndex(member => member.id === activeMemberId);
    let bioInsertIndex = -1;

    if (activeMemberIndex !== -1) {
      // Calculate the end of the row containing the active member
      const rowNumber = Math.floor(activeMemberIndex / cols);
      bioInsertIndex = Math.min((rowNumber + 1) * cols - 1, boardMembers.length - 1);
    }

    boardMembers.forEach((member, index) => {
      result.push(createCardHTML(member));

      if (index === bioInsertIndex && activeMemberId) {
        const activeMember = boardMembers.find(m => m.id === activeMemberId);
        result.push(
          <div key={`bio-${activeMemberId}`} className="bio-details-container active col-span-1 md:col-span-2 lg:col-span-3">
            <div className="bio-content-wrapper">
              {createBioHTML(activeMember)}
            </div>
          </div>
        );
      }
    });

    return result;
  };

  return (
    <div style={{ backgroundColor: '#012854' }} className="text-white font-['Inter',sans-serif]">
      <div className="container mx-auto p-4 md:p-8 max-w-7xl">
        <header className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white text-center">Our Leadership</h1>
        </header>

        <div className="mb-6">
          {createTeamImageWithZones()}
        </div>

        <div className="flex justify-center w-full">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-1 max-w-5xl w-full">
            {renderMembersGrid()}
          </div>
        </div>
      </div>
    </div>
  );
};
 
export default TeamPage;