import React, { useState, useEffect } from "react";
import { FaLinkedin, FaEnvelope, FaTimes } from "react-icons/fa";
 
const TeamPage = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      description:
        "Serial Entrepreneur, over 18 years of experience managing technology business.",
      linkedin: "linkedin.com/in/krishna-samanth-bb0a46b5/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Du<PERSON> <PERSON>",
      role: "COO & Co-Founder",
      description:
        "Over 30 years of experience in Global Software Delivery, Vision, Strategy and Operational Excellence.",
      linkedin: "linkedin.com/in/durga-prasad-a-632ab61a1/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "<PERSON><PERSON>",
      role: "CHRO & Co-Founder - Dubai",
      description:
        "30 years in Global HR functions for IT (Products / Projects) and Retail sectors.",
      linkedin: "linkedin.com/in/sc007/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Vamshree",
      role: "CTO & Managing Partner - Australia",
      description:
        "20 years in IT Development and Enterprise Delivery across Finance, Banking, Telecom, and Healthcare.",
      linkedin: "linkedin.com/in/vamshree/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Sreeni Raju",
      role: "Head – ERP Software Solutions & Delivery - USA",
      description:
        "22 Years of experience in core HR, global ERP implementations and consulting in some of the global fortune 500 companies.",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Nishant Seth",
      role: "Practice Head- Data, AI/ML and Analytics",
      description:
        "Data Analytics consultant with 25+ years in AI/ML, data lakes, and analytics across industries.",
      linkedin: "linkedin.com/in/nishantseth/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Kishore Seetharam",
      role: "Head - Talent Acquisition",
      description: "Over 17 Years of Experience in Technical Recruitment.",
      linkedin: "linkedin.com/in/seetharam-kishore-25a9881a/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Dr. Mehala N",
      role: "Advisor",
      description:
        "20+ years in academia, teaching, content development, and research in Data Science & Machine Intelligence.",
      linkedin: "linkedin.com/in/dr-mehala-n-********/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Vinesh Singh",
      role: "Head - L&D",
      description:
        "Over 35 years in Finance, Accounting, strategic planning, Budgeting, Joint ventures and Vendor management",
      linkedin: "https://www.linkedin.com/in/vineshsingh/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Sai Krishna",
      role: "Head - Product & Innovation",
      description:
        "Over 13+ years of experience managing research and development in IoT",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
  ];
 
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [selectedMember, setSelectedMember] = useState(null);
 
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
 
  const isDesktop = windowWidth > 1024;
  const isTablet = windowWidth >= 600 && windowWidth <= 1024;
  const isMobile = windowWidth < 600;
 
  const cardsPerRow = isDesktop ? 4 : isTablet ? 3 : 2;
 
  // Original baseCardWidth
  const originalBaseCardWidth = 200;
  // Increase width by 15%
  const baseCardWidth = originalBaseCardWidth * 1.15; // Current width is 230px
 
  // Adjusted gap
  const gap = 40; // You can adjust this value further for more or less gap
 
  // maxContainerWidthFor4Cards is now dynamically set in the JSX below
 
  const Card = ({ member }) => (
    <div
      onClick={isMobile ? () => setSelectedMember(member) : undefined}
      onMouseEnter={isMobile ? ((e) => {
        e.currentTarget.style.transform = "scale(1.05)";
        e.currentTarget.style.boxShadow =
          "0 10px 30px rgba(0,0,0,0.5), inset 0 0 20px rgba(255,255,255,0.1)";
      }) : undefined}
      onMouseLeave={isMobile ? ((e) => {
        e.currentTarget.style.transform = "scale(1)";
        e.currentTarget.style.boxShadow =
          "0 4px 12px rgba(0,0,0,0.25), inset 0 0 15px rgba(255,255,255,0.05)";
      }) : undefined}
      style={{
        background: "#fff",
        borderRadius: 16,
        padding: isMobile ? "0.8rem 0.8rem 1rem" : "1.2rem 1.2rem 1.5rem",
        boxShadow:
          "0 4px 12px rgba(0,0,0,0.25), inset 0 0 15px rgba(255,255,255,0.05)",
        display: "flex",
        flexDirection: "column",
        transition: "transform 0.3s ease, box-shadow 0.3s ease",
        cursor: isMobile ? "pointer" : "default",
        minHeight: isMobile ? 300 : 'auto',
        boxSizing: "border-box",
 
        flexGrow: 1,
        flexShrink: 0,
        flexBasis: `calc(${100 / cardsPerRow}% - ${gap - (gap / cardsPerRow)}px)`,
        maxWidth: baseCardWidth, // Apply the new baseCardWidth
        minWidth: isMobile ? '120px' : '180px',
        height: isDesktop ? '380px' : 'auto',
      }}
    >
      <img
        src={member.image}
        alt={member.name}
        style={{
          width: isMobile ? 90 : 140,
          height: isMobile ? 90 : 140,
          borderRadius: "50%",
          objectFit: "cover",
          margin: isMobile ? "0 auto 0.7rem" : "0 auto 1rem",
          border: "3px solid #00a0e9",
          filter: "grayscale(5%) contrast(1.05)",
          transition: "filter 0.3s ease",
          flexShrink: 0,
        }}
      />
      <h5
        style={{
          color: "gray",
          fontWeight: "700",
          fontSize: isMobile ? "0.9rem" : "1.1rem",
          letterSpacing: 0.8,
          marginBottom: isMobile ? 2 : 4,
          textAlign: "center",
          wordBreak: 'break-word',
        }}
      >
        {member.name}
      </h5>
      <h6
        style={{
          color: "#000",
          fontWeight: "600",
          fontSize: isMobile ? "0.75rem" : "0.9rem",
          marginBottom: isMobile ? 4 : 8,
          textAlign: "center",
          wordBreak: 'break-word',
        }}
      >
        {member.role}
      </h6>
      <p
        style={{
          color: "#000",
          fontSize: isMobile ? "0.68rem" : "0.85rem",
          lineHeight: 1.3,
          fontWeight: "400",
          flexGrow: 1,
          marginBottom: isMobile ? 6 : 12,
          textAlign: "center",
          wordBreak: 'break-word',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitLineClamp: isMobile ? 5 : 'unset',
          WebkitBoxOrient: 'vertical',
        }}
      >
        {member.description}
      </p>
      {(member.linkedin || member.email) && (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            gap: isMobile ? 6 : 10,
            marginTop: "auto",
          }}
        >
          {member.linkedin && (
            <a
              href={`https://${member.linkedin}`}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={`LinkedIn profile of ${member.name}`}
              onClick={(e) => e.stopPropagation()}
              style={{
                ...iconStyle,
                fontSize: isMobile ? "0.9rem" : "1.1rem",
                padding: isMobile ? 5 : 7,
                width: isMobile ? 28 : 32,
                height: isMobile ? 28 : 32,
              }}
              onMouseEnter={iconHoverIn}
              onMouseLeave={iconHoverOut}
            >
              <FaLinkedin style={{ color: "gray" }} />
            </a>
          )}
          {member.email && (
            <a
              href={`mailto:${member.email}`}
              aria-label={`Send email to ${member.name}`}
              onClick={(e) => e.stopPropagation()}
              style={{
                ...iconStyle,
                fontSize: isMobile ? "0.9rem" : "1.1rem",
                padding: isMobile ? 5 : 7,
                width: isMobile ? 28 : 32,
                height: isMobile ? 28 : 32,
              }}
              onMouseEnter={iconHoverIn}
              onMouseLeave={iconHoverOut}
            >
              <FaEnvelope style={{ color: "gray" }} />
            </a>
          )}
        </div>
      )}
    </div>
  );
 
  const iconStyle = {
    color: "#00a0e9",
    background: "rgba(168, 168, 168, 0.15)",
    borderRadius: 8,
    border: "1.3px solid rgba(80, 80, 80, 0.3)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transition: "all 0.3s ease",
    cursor: "pointer",
  };
 
  function iconHoverIn(e) {
    e.currentTarget.style.background = "rgba(0, 160, 233, 0.4)";
    e.currentTarget.style.color = "#fff";
    e.currentTarget.style.boxShadow = "0 6px 18px rgba(0, 160, 233, 0.6)";
  }
 
  function iconHoverOut(e) {
    e.currentTarget.style.background = "rgba(0, 160, 233, 0.15)";
    e.currentTarget.style.color = "#00a0e9";
    e.currentTarget.style.boxShadow = "0 2px 8px rgba(0, 160, 233, 0.25)";
  }
 
  return (
    <div
      style={{
        minHeight: "100vh",
        background: "linear-gradient(180deg, #001533 0%, #002a70 100%)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "4rem 1.5rem 6rem",
        fontFamily: "'Poppins', sans-serif",
      }}
    >
      <div
        style={{
          maxWidth: 900, // This max-width is for the text header, not the cards container
          textAlign: "center",
          marginBottom: "2.5rem",
          padding: "0 1rem",
        }}
      >
        <h1
          style={{
            fontSize: "3.6rem",
            fontWeight: "800",
            letterSpacing: "2.6px",
            marginBottom: "1rem",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}
        >
          Our Leadership
        </h1>
        <p
          style={{
            color: "#cbd5e1",
            fontSize: "1.2rem",
            lineHeight: "1.9",
            margin: "0 auto",
            fontWeight: 500,
            opacity: 0.9,
          }}
        >
          Since its inception, Makonis has thrived under exceptional leadership — from
          our strong, engaged, and independent board members to our experienced and
          globally distributed senior management team.
        </p>
      </div>
 
      <div
        style={{
          display: "flex",
          flexWrap: "wrap",
          justifyContent: "center",
          gap: gap,
          width: '100%',
          boxSizing: 'border-box',
          margin: '0 auto', // Centers the grid horizontally
          padding: isDesktop ? '0 2rem' : '0 1rem', // Padding for visual spacing on desktop
          alignItems: 'stretch', // Ensures all cards in a row have the same height
 
          // DYNAMIC MAX-WIDTH for the cards container
          maxWidth: isDesktop ? '1400px' : '100%', // Adjust '1400px' as desired for larger screens
        }}
      >
        {teamMembers.map((member, idx) => (
          <Card member={member} key={idx} />
        ))}
      </div>
 
      {/* Modal/Overlay for Expanded Member Details */}
      {selectedMember && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
            backdropFilter: 'blur(5px)',
          }}
          onClick={() => setSelectedMember(null)}
        >
          <div
            style={{
              background: '#fff',
              borderRadius: 16,
              padding: isMobile ? "1.5rem" : "2.5rem",
              margin: '1rem',
              maxWidth: isMobile ? '90%' : '500px',
              width: '100%',
              boxShadow: '0 10px 30px rgba(0,0,0,0.5)',
              position: 'relative',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              overflowY: 'auto',
              maxHeight: '90vh',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setSelectedMember(null)}
              style={{
                position: 'absolute',
                top: isMobile ? '0.8rem' : '1.5rem',
                right: isMobile ? '0.8rem' : '1.5rem',
                background: 'none',
                border: 'none',
                fontSize: isMobile ? "1.5rem" : "1.8rem",
                color: '#333',
                cursor: 'pointer',
                padding: '0.2rem',
              }}
            >
              <FaTimes />
            </button>
            <img
              src={selectedMember.image}
              alt={selectedMember.name}
              style={{
                width: isMobile ? 100 : 150,
                height: isMobile ? 100 : 150,
                borderRadius: "50%",
                objectFit: "cover",
                marginBottom: isMobile ? "1rem" : "1.5rem",
                border: "4px solid #00a0e9",
                filter: "grayscale(0%) contrast(1)",
              }}
            />
            <h5
              style={{
                color: "gray",
                fontWeight: "700",
                fontSize: isMobile ? "1.4rem" : "1.8rem",
                letterSpacing: 0.8,
                marginBottom: isMobile ? 0.5 : 0.8,
                textAlign: "center",
              }}
            >
              {selectedMember.name}
            </h5>
            <h6
              style={{
                color: "#000",
                fontWeight: "600",
                fontSize: isMobile ? "1.1rem" : "1.3rem",
                marginBottom: isMobile ? 1 : 1.5,
                textAlign: "center",
              }}
            >
              {selectedMember.role}
            </h6>
            <p
              style={{
                color: "#000",
                fontSize: isMobile ? "0.9rem" : "1.05rem",
                lineHeight: 1.5,
                fontWeight: "400",
                marginBottom: isMobile ? 1.5 : 2,
                textAlign: "center",
                wordBreak: 'break-word',
                whiteSpace: 'pre-wrap',
                width: '100%',
                boxSizing: 'border-box',
              }}
            >
              {selectedMember.description}
            </p>
            {(selectedMember.linkedin || selectedMember.email) && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  gap: isMobile ? 15 : 20,
                  marginTop: "auto",
                }}
              >
                {selectedMember.linkedin && (
                  <a
                    href={`https://${selectedMember.linkedin}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={`LinkedIn profile of ${selectedMember.name}`}
                    style={{
                      ...iconStyle,
                      fontSize: isMobile ? "1.5rem" : "1.8rem",
                      padding: isMobile ? 8 : 10,
                      width: isMobile ? 40 : 45,
                      height: isMobile ? 40 : 45,
                      background: 'rgba(0, 160, 233, 0.2)',
                      borderColor: 'rgba(0, 160, 233, 0.5)',
                    }}
                    onMouseEnter={iconHoverIn}
                    onMouseLeave={iconHoverOut}
                  >
                    <FaLinkedin style={{ color: "#00a0e9" }} />
                  </a>
                )}
                {selectedMember.email && (
                  <a
                    href={`mailto:${selectedMember.email}`}
                    aria-label={`Send email to ${selectedMember.name}`}
                    style={{
                      ...iconStyle,
                      fontSize: isMobile ? "1.5rem" : "1.8rem",
                      padding: isMobile ? 8 : 10,
                      width: isMobile ? 40 : 45,
                      height: isMobile ? 40 : 45,
                      background: 'rgba(0, 160, 233, 0.2)',
                      borderColor: 'rgba(0, 160, 233, 0.5)',
                    }}
                    onMouseEnter={iconHoverIn}
                    onMouseLeave={iconHoverOut}
                  >
                    <FaEnvelope style={{ color: "#00a0e9" }} />
                  </a>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
 
export default TeamPage;