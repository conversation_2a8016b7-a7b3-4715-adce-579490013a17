import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { debounce } from '../contexts/searchUtils';
import { popularSearches } from '../contexts/searchData';

const SearchOverlay = ({ isOpen, onClose }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isLoading, setIsLoading] = useState(false);
  
  const inputRef = useRef(null);
  const overlayRef = useRef(null);
  const navigate = useNavigate();

  // Debounced search function
  const debouncedSearch = useRef(
    debounce(async (searchQuery) => {
      if (!searchQuery || searchQuery.trim().length < 2) {
        setSuggestions([]);
        setIsLoading(false);
        return;
      }

      try {
        const { getSearchSuggestions } = await import('../contexts/searchUtils');
        const results = getSearchSuggestions(searchQuery.trim(), 8);
        setSuggestions(results);
      } catch (error) {
        console.error('Search error:', error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, 300)
  ).current;

  // Handle input change
  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    setSelectedIndex(-1);
    setIsLoading(true);
    
    if (value.trim().length > 0) {
      debouncedSearch(value);
    } else {
      setSuggestions([]);
      setIsLoading(false);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    const totalSuggestions = suggestions.length;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < totalSuggestions - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : totalSuggestions - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && suggestions[selectedIndex]) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else if (query.trim()) {
          handleSearch();
        }
        break;
      case 'Escape':
        e.preventDefault();
        handleClose();
        break;
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    navigate(suggestion.path);
    handleClose();
  };

  // Handle search submission - removed redirect, just close overlay
  const handleSearch = () => {
    // Don't redirect to search page, just close the overlay
    // Users should select from suggestions instead
    handleClose();
  };

  // Handle popular search click - trigger search to show results
  const handlePopularSearchClick = (searchTerm) => {
    setQuery(searchTerm);
    setIsLoading(true);
    debouncedSearch(searchTerm);
  };

  // Handle close
  const handleClose = () => {
    setQuery('');
    setSuggestions([]);
    setSelectedIndex(-1);
    setIsLoading(false);
    onClose();
  };

  // Handle overlay click
  const handleOverlayClick = (e) => {
    if (e.target === overlayRef.current) {
      handleClose();
    }
  };

  // Auto focus when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      // Small delay to ensure the overlay is fully rendered
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Prevent body scroll when overlay is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      ref={overlayRef}
      className="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-start justify-content-center"
      style={{
        backgroundColor: 'rgba(0, 41, 86, 0.95)',
        backdropFilter: 'blur(10px)',
        zIndex: 9999,
        paddingTop: '10vh',
        animation: 'fadeIn 0.3s ease-out'
      }}
      onClick={handleOverlayClick}
    >
      <div 
        className="w-100 px-4"
        style={{ maxWidth: '600px' }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="d-flex align-items-center justify-content-between mb-4">
          <h2 className="text-white mb-0 h4">Search Makonis</h2>
          <button
            onClick={handleClose}
            className="btn btn-link text-white p-2"
            style={{
              minWidth: '44px',
              minHeight: '44px',
              border: 'none',
              background: 'none'
            }}
            aria-label="Close search"
          >
            <lord-icon
              src="https://cdn.lordicon.com/nqtddedc.json"
              trigger="hover"
              colors="primary:#ffffff"
              style={{ width: '24px', height: '24px' }}>
            </lord-icon>
          </button>
        </div>

        {/* Search Input */}
        <div className="position-relative mb-4">
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Search services, pages, technologies..."
            className="w-100 px-5 py-4 border-0 rounded-3xl text-lg bg-white/10 text-white placeholder-white/70 backdrop-blur-sm"
            style={{
              outline: 'none',
              boxShadow: '0 0 0 2px rgba(255, 255, 255, 0.2)',
              transition: 'all 0.3s ease'
            }}
            onFocus={(e) => {
              e.target.style.boxShadow = '0 0 0 2px rgba(0, 160, 233, 0.5)';
            }}
            onBlur={(e) => {
              e.target.style.boxShadow = '0 0 0 2px rgba(255, 255, 255, 0.2)';
            }}
            autoComplete="off"
          />
          
          <div className="position-absolute top-50 end-0 translate-middle-y pe-4">
            {isLoading ? (
              <div className="spinner-border spinner-border-sm text-white" role="status">
                <span className="visually-hidden">Searching...</span>
              </div>
            ) : (
              <button
                type="button"
                onClick={query.trim() ? handleSearch : undefined}
                className="btn btn-link p-0 text-white/70 hover:text-white transition-colors duration-200"
                disabled={!query.trim()}
                aria-label="Search"
              >
                <lord-icon
                  src="https://cdn.lordicon.com/xfftupfv.json"
                  trigger="hover"
                  colors="primary:#ffffff"
                  style={{ width: '20px', height: '20px' }}>
                </lord-icon>
              </button>
            )}
          </div>
        </div>

        {/* Results */}
        <div 
          className="bg-white/10 backdrop-blur-xl rounded-2xl overflow-hidden"
          style={{ maxHeight: '60vh', overflowY: 'auto' }}
        >
          {suggestions.length > 0 ? (
            <>
              <div className="px-4 py-3 border-bottom border-white/20">
                <small className="text-white/70 fw-semibold">Suggestions</small>
              </div>
              {suggestions.map((suggestion, index) => (
                <div
                  key={suggestion.id}
                  className={`px-4 py-3 cursor-pointer transition-all duration-200 ${
                    index === selectedIndex 
                      ? 'bg-white/20' 
                      : 'hover:bg-white/10'
                  }`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  style={{ borderBottom: index < suggestions.length - 1 ? '1px solid rgba(255, 255, 255, 0.1)' : 'none' }}
                >
                  <div className="d-flex align-items-center">
                    <i className={`${suggestion.icon} text-makonis-secondary me-3`} style={{ width: '20px' }}></i>
                    <div className="flex-grow-1">
                      <div className="text-white fw-semibold">{suggestion.title}</div>
                      <div className="text-white/70 small text-capitalize">{suggestion.type}</div>
                    </div>
                    <lord-icon
                      src="https://cdn.lordicon.com/zmkotitn.json"
                      trigger="hover"
                      colors="primary:#ffffff80"
                      style={{ width: '16px', height: '16px' }}>
                    </lord-icon>
                  </div>
                </div>
              ))}
            </>
          ) : query.trim().length === 0 ? (
            <>
              <div className="px-4 py-3 border-bottom border-white/20">
                <small className="text-white/70 fw-semibold">Popular Searches</small>
              </div>
              {popularSearches.slice(0, 6).map((searchTerm, index) => (
                <div
                  key={index}
                  className="px-4 py-3 cursor-pointer hover:bg-white/10 transition-all duration-200"
                  onClick={() => handlePopularSearchClick(searchTerm)}
                  style={{ borderBottom: index < 5 ? '1px solid rgba(255, 255, 255, 0.1)' : 'none' }}
                >
                  <div className="d-flex align-items-center">
                    <lord-icon
                      src="https://cdn.lordicon.com/abfverha.json"
                      trigger="hover"
                      colors="primary:#ffffff80"
                      style={{ width: '20px', height: '20px', marginRight: '12px' }}>
                    </lord-icon>
                    <div className="text-white">{searchTerm}</div>
                  </div>
                </div>
              ))}
            </>
          ) : (
            <div className="px-4 py-8 text-center">
              <lord-icon
                src="https://cdn.lordicon.com/xfftupfv.json"
                trigger="hover"
                colors="primary:#ffffff80"
                style={{ width: '48px', height: '48px', display: 'block', marginBottom: '12px' }}>
              </lord-icon>
              <div className="text-white mb-2">No suggestions found</div>
              <small className="text-white/70">Try a different search term</small>
            </div>
          )}
        </div>

        {/* Keyboard shortcuts hint */}
        <div className="mt-4 text-center">
          <small className="text-white/50">
            Use ↑↓ to navigate, Enter to select, Esc to close
          </small>
        </div>
      </div>
    </div>
  );
};

export default SearchOverlay;
